<?php
/**
 * This file contains the code to replace the attributes table in product_edit.php
 * with a modular implementation.
 * 
 * To apply this patch:
 * 1. Include this file at the top of product_edit.php
 * 2. Replace the attributes table section with a call to draw_attributes_table()
 */

// Include the required files
require_once("includes/functions/tcs_attributes_components.php");

/**
 * Draw the attributes table for a product
 * 
 * @param int $products_id The product ID
 * @return string The HTML for the attributes table
 */
function draw_attributes_table($products_id) {
    global $languages_id;
    
    // Create the attributes object
    $products_attributes = new tcs_product_attributes((int)$products_id);
    
    // Get the attributes
    $attributes = $products_attributes->get_attributes(false);
    
    // Output the table
    if (!empty($products_id)) {
        $output = '<div class="col-xs-10">';
        $output .= tcs_draw_attributes_table($products_attributes, $products_id);
        $output .= '</div>';
        
        // Include the JavaScript for the attributes table
        $output .= '<script src="includes/javascript/attributes_table.js"></script>';
        
        return $output;
    } else {
        return '<div class="col-xs-10">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <strong>Save first to generate product id</strong>
                    </div>
                </div>
            </div>';
    }
}
?>
