/**
 * @license Copyright (c) 2003-2018, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see https://ckeditor.com/legal/ckeditor-oss-license
 */

CKEDITOR.editorConfig = function( config ) {
	// Define changes to default configuration here. For example:
	// config.language = 'fr';
	// config.uiColor = '#AADC6E';
		// Define changes to default configuration here. For example:
	// config.language = 'fr';
	// config.uiColor = '#AADC6E';
    config.font_names = 'Andale Mono;' +
                        'Arial;' +
                        'Arial Black;' +
                        'Book Antiqua;' +
                        'Comic Sans MS;' +
                        'Courier New;' +
                        'Georgia;' +
                        'Helvetica;' +
                        'Impact;' +
                        'Symbol;' +
                        'Tahoma;' +
                        'Terminal;' +
                        'Times New Roman;' +
                        'Trebuchet MS;' +
                        'Verdana;' +
                        'Webdings;' +
                        'Wingdings;';
    config.height = '50em';
    config.allowedContent = true;
    config.extraPlugins = 'imageuploader';
};
