<?php

$imagebrowser1 = "Image Browser for CKEditor";
$imagebrowser2 = "In total:";
$imagebrowser3 = "Images";
$imagebrowser4 = "Drop your files here";

$uploadpanel1 = "Please select a file:";
$uploadpanel2 = "The image will be uploaded to:";
$uploadpanel3 = "The upload path can be changed in the settings";

$panelsettings1 = "Upload path:";
$panelsettings2 = "Please choose an existing folder:";
$panelsettings3 = "Path history:";
$panelsettings4 = "Settings:";
$panelsettings5 = "Hide file extension";
$panelsettings6 = "Show file extension";
$panelsettings7 = "Password:";
$panelsettings8 = "Logout";
$panelsettings9 = "Disable password";
$panelsettings10 = "Do you like our plugin?";
$panelsettings11 = "Buy Us A Coffee!";
$panelsettings12 = "Support:";
$panelsettings13 = "Plugin FAQ";
$panelsettings14 = "Report a bug";
$panelsettings15 = "Version:";
$panelsettings16 = "Credits:";
$panelsettings17 = "Made with love by <PERSON><PERSON>";
$panelsettings18 = "Icons:";
$panelsettings19 = "Icon pack by Icons8";
$panelsettings20 = "Change language";
$panelsettings21 = "Hide news section";
$panelsettings22 = "Show news section";

$newssection1 = "You can disable the news section in the settings panel.";

$buttons1 = "View";
$buttons2 = "Download";
$buttons3 = "Use";
$buttons4 = "Delete";
$buttons5 = "Cancel";
$buttons6 = "Save";
$buttons7 = "Upload";

$alerts1 = "Thanks for choosing Image Uploader and Browser for CKEditor!";
$alerts2 = "To use this plugin you need to set <b>CHMOD writable permission (0777)</b> to the <i>imageuploader</i> folder on your server.";
$alerts3 = "How to Change File Permissions Using FileZilla (external link)";
$alerts4 = "Check out the <a href='http://imageuploaderforckeditor.altervista.org/' target='_blank'>Documentation</a> or the <a href='http://imageuploaderforckeditor.altervista.org/support/' target='_blank'>Plugin FAQ</a> for more help.";
$alerts5 = "To use this plugin you need to enable <b>JavaScript</b> in your web browser.";
$alerts6 = "How to enable JavaScript in your browser (external link)";
$alerts7 = "A new version of Image Uploader and Browser for CKEditor is available.";
$alerts8 = "Download it now!";
$alerts9 = "The folder";
$alerts10 = "could not be found.";
$alerts11 = "create the folder";

$dltimageerrors1 = "An error occurred.";
$dltimageerrors2 = "You can only delete images. Please try again or delete another image.";
$dltimageerrors3 = "The file you want to delete is not in the selected upload folder.";
$dltimageerrors4 = "You cannot delete sytem files. Please try again or choose another image.";
$dltimageerrors5 = "The selected file cannot be deleted. Please try again or choose another image. Note: Don not forget to set CHMOD writable permission (0777) to the imageuploader folder on your server.";
$dltimageerrors6 = "The file you want to delete does not exist. Please try again or choose another image.";

$uploadimgerrors1 = "File is not an image.";
$uploadimgerrors2 = "Sorry, file already exists.";
$uploadimgerrors3 = "Sorry, your file is too large.";
$uploadimgerrors4 = "Sorry, only JPG, JPEG, PNG & GIF files are allowed.";
$uploadimgerrors5 = "Sorry, your file was not uploaded. Don't forget to set CHMOD writable permission (0777) to imageuploader folder on your server.";
$uploadimgerrors6 = "Sorry, there was an error uploading your file -";
$uploadimgerrors7 = "- Don't forget to set CHMOD writable permission (0777) to imageuploader folder on your server.";

$loginerrors1 = "No user found, incorrect password or username!";

$configerrors1 = "Please use the plugin settings to change the visibility or try again.";
$configerrors2 = "Please use the image browser to change the file style or try again.";

$loginsite1 = "Welcome!";
$loginsite2 = "Please log in.";
$loginsite3 = "Username";
$loginsite4 = "Password";
$loginsite5 = "Log in";

$createaccount1 = "Please create a new (local) account to prevent others to view and manage your images.";
$createaccount2 = "How can I disable the password protection? (external link)";
$createaccount3 = "The default upload folder is <b>ckeditor/plugins/imageuploader/uploads</b>. You can change it in the settings panel.";

$langpanel1 = "Please select a language:";
$langpanel2 = "Currently selected:";
$langpanel3 = "Close";