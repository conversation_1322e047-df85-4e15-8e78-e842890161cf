<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2017 osCommerce

  Released under the GNU General Public License
*/
?><?php
    $position = 1;
	$lastTable = -1;
    while ($relatedProducts = tep_db_fetch_array($get_related_products_query)) { 
		if ($lastTable != $relatedProducts['products_related_table_id']) {
				if ($relatedProducts['products_related_table_id'] > 0) {?>					
					</tbody></table>				
				<?php }
					++$lastTable;
				?>
 <table id="relatedProductsModule-<?php echo $relatedProducts['products_related_table_id'] ?>" cellspacing="3" cellpadding="3" class="table table-striped table-hover table-condensed small" width="100%">
 <thead class="thead-dark">
	<tr>
	  <th>Model</th>
	  <th>Product</th>
	  <th class="text-center">Price</th>
	  <th class="text-center">Add To Basket</th>
	</tr>
 </thead>
		<tbody id="relatedProductsTableBody-<?php echo $relatedProducts['products_related_table_id'] ?>">
			<?php } ?>
         
		<tr>
			<?php   
					$variations = new tcs_product_attributes($relatedProducts['products_id']);	
					//print_rr($variations,'vari13');				
					if ($variations->has_variations){
						$variationList = $variations->get_variations();
						
						$related_variations_q = tep_db_query("select * from products_variations_to_products_related WHERE products_id = '" . $relatedProducts['products_id'] . "' AND products_related_id='" . $relatedProducts['products_related_id']  . "'");
			
						while ($related_variations = tep_db_fetch_array($related_variations_q)) {
							$related_variations_status[$related_variations['products_related_id']][$related_variations['products_variations_id']] = $related_variations['status'];
						}
						print_rr($related_variations_status,'poop');
						foreach($variationList as $i=>$variation){
							$check_status = @$related_variations_status[$relatedProducts['products_related_id']][$variation['products_variations_id']];
							print_rr($check_status,'check_status');
							if ($check_status === '0'){
									continue;
							} else { ?>
								<tr>
								   <td nowrap><?php echo $variation['model'] ?></td>
								   <td><a itemprop="url" href="<?php echo tep_href_link('product_info.php', 'products_id=' . (int)$relatedProducts['products_id'] . $variation['attribute_string'] ); ?>"><span itemprop="name"><?php echo $relatedProducts['products_name'] . $variation['product_name_suffix'] ?></span></a><meta itemprop="position" content="<?php echo (int)$position; ?>" /></td>
								   <td class="text-center"><?php
							  if ($variation['price'] > 0) {
								if (@tep_not_null($variation['specials_new_price'])) {?>
								  <div class="" itemprop="offers" itemscope itemtype="http://schema.org/Offer"><meta itemprop="priceCurrency" content="<?php echo tep_output_string($currency)?>/><del><?php echo $currencies->display_price($variation['price'], tep_get_tax_rate($variation['products_tax_class_id']));?></del>&nbsp;&nbsp;<span class="productSpecialPrice" itemprop="price" content="'<?php echo $currencies->display_raw($variation['price'], tep_get_tax_rate($variation['products_tax_class_id']))?>"><?php echo $currencies->display_price($variation['specials_new_price'], tep_get_tax_rate($variation['products_tax_class_id']))?></span></div>
								<?php } else { ?>
								  <div class="" itemprop="offers" itemscope itemtype="http://schema.org/Offer"><meta itemprop="priceCurrency" content="<?php echo tep_output_string($currency)?>" /><span itemprop="price" content="<?php echo  $currencies->display_raw($variation['price'], tep_get_tax_rate($variation['products_tax_class_id']))?>"><?php echo $currencies->display_price($variation['price'], tep_get_tax_rate($variation['products_tax_class_id']))?></span></div>
									<?php }
									}
								   ?>
								   </td>  <td><div class="col-xs-8 text-right">
														   <?php
														  if ($variation['price']){
												echo  '' . tep_draw_button('Add', 'fa fa-shopping-cart', tep_href_link(basename($PHP_SELF),'action=buy_now&products_id=' . (int)$relatedProducts['products_id'] . $variation['attribute_string']), NULL, array('type'=>'button', 'params'=>'data-products-id="' . $relatedProducts["products_id"]. '" data-attributes="' . $variation['attribute_string']. '"'), 'btn-success btn-buy btn-xs') . '';
										} else {
												echo  '' . tep_draw_button('Call for Price', 'fa fa-shopping-cart','', NULL, NULL, 'btn-success btn-sm') . '';
										}?>
								   
								   
								   
								   </div>
		</td>
								</tr>    
							  <?php
							}
						 }
					} else{?>			
				
           <td nowrap><?php echo $relatedProducts['products_model'] ?></td>
		   <td><a itemprop="url" href="<?php echo tep_href_link('product_info.php', 'products_id=' . $relatedProducts['products_id']); ?>"><span itemprop="name"><?php echo $relatedProducts['products_name']; ?></span></a><meta itemprop="position" content="<?php echo (int)$position; ?>" /></td>
			

		<td class="text-center"><?php
      if ($relatedProducts['products_price'] > 0) {              
        if (@tep_not_null($relatedProducts['specials_new_products_price'])) {?>
          <div class="" itemprop="offers" itemscope itemtype="http://schema.org/Offer"><meta itemprop="priceCurrency" content="<?php echo tep_output_string($currency)?>/><del><?php echo $currencies->display_price($relatedProducts['products_price'], tep_get_tax_rate($relatedProducts['products_tax_class_id']));?></del>&nbsp;&nbsp;<span class="productSpecialPrice" itemprop="price" content="'<?php echo $currencies->display_raw($relatedProducts['products_price'], tep_get_tax_rate($relatedProducts['products_tax_class_id']))?>"><?php echo $currencies->display_price($relatedProducts['specials_new_products_price'], tep_get_tax_rate($relatedProducts['products_tax_class_id']))?></span></div>
        <?php } else { ?>
          <div class="" itemprop="offers" itemscope itemtype="http://schema.org/Offer"><meta itemprop="priceCurrency" content="<?php echo tep_output_string($currency)?>" /><span itemprop="price" content="<?php echo  $currencies->display_raw($relatedProducts['products_price'], tep_get_tax_rate($relatedProducts['products_tax_class_id']))?>"><?php echo $currencies->display_price($relatedProducts['products_price'], tep_get_tax_rate($relatedProducts['products_tax_class_id']))?></span></div>
			<?php }
			}
		   ?>
		   </td>
		   <td>
	 <?php if ($relatedProducts['products_request_quote'] == '1') {?>
		<?php	/*	<div class="col-xs-8 text-right"><?php echo tep_draw_button('Ask&nbsp;for&nbsp;Quote', 'fa fa-commenting-o', null, null,array('type'=>'button', 'params'=>'data-toggle="modal" data-target="#fancyForm" data-product="' . $relatedProducts['products_name'] . '"'),"btn-success btn-xs requestQuote");</div> */ ?>
	 <?php } else {
				if ($relatedProducts['products_price'] > 0){ ?>
					<div class="col-xs-8 text-right"><?php echo tep_draw_button('Add', 'fa fa-shopping-cart', tep_href_link(basename($PHP_SELF), 'action=buy_now&products_id=' . $relatedProducts['products_id']), NULL, array('type'=>'button', 'params'=>'data-products-id="' . $relatedProducts["products_id"]. '" data-attributes="' . $variation['attribute_string']. '"'), 'btn-success btn-buy btn-xs btn-buy'); ?></div>
		  <?php } else { ?>
					<?php //<div class="col-xs-8 text-right"><?php echo tep_draw_button('Call for Price', 'fa fa-shopping-cart','', NULL, NULL, 'btn-success btn-sm btn-xs'); </div>*/  ?>          
  		<?php }          
			}
					}?>
	  </td>
		</tr>      
      <?php
      $position++;
    }
    ?>    
	</tbody>
	</table> 