<?php
// Capture the raw POST data
$rawPayload = file_get_contents('php://input');

require_once 'includes/application_top.php';
require_once 'includes/classes/autodesk_wsapi_guzzle.php';

/*{
'id':'512936b1-a31b-4870-9412-0b76d9a6fcdd'
'topic':'quote-status'
'event':'changed'
'sender':'PWS Quote Status'
'publishedAt':'2024-08-08T13:44:49.355Z'
'csn':'5103159758'
'environment':'stg'
'payload':{
'quoteNumber':'Q-00859'
'quoteStatus':'Ordered'
'transactionId':'13847669-f969-4e2a-bfba-a9ac3c051a21'
'message':'Quote# Q-01522 status changed to Ordered.'
'modifiedAt':'2024-08-08T13:44:49.355Z'
}
}*/

// Decode the JSON payload into a PHP associative array
$payload = json_decode($rawPayload, true);

// Extract headers
$headers = getallheaders();

echo $payload;

// Process the data (you can customize this part as needed)
$quoteNumber = $payload['payload']['quoteNumber'];
$quoteStatus = $payload['payload']['quoteStatus'];
$transactionId = $payload['payload']['transactionId'];
$message = $payload['payload']['message'];
$modifiedAt = $payload['payload']['modifiedAt'];
$csn = $payload['csn'];
$publishedAt = $payload['publishedAt'];

// // Example: Logging the received data
// error_log('Quote Number: $quoteNumber');
// error_log('Quote Status: $quoteStatus');
// error_log('Transaction ID: $transactionId');
// error_log('Message: $message');
// error_log('Modified At: $modifiedAt');
// error_log('CSN: $csn');
// error_log('Published At: $publishedAt');

// Example' = > Responding to the webhook
header('Content-Type: application/json');
echo json_encode([
   'Quote Number' => $quoteNumber,
    'Quote Status' => $quoteStatus,
    'Transaction ID' => $transactionId,
    'Message' => $message,
    'Modified At' => $modifiedAt,
    'CSN' => $csn,
    'Published At' => $publishedAt,    
    'status' => 'success'
]);

?>