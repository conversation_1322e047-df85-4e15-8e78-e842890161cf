

@media screen and (max-width: 993px) {
  .sidebar-offcanvas {
	padding-top: 60px;
    -webkit-transition: all .25s ease-out;
         -o-transition: all .25s ease-out;
            transition: all .25s ease-out;
			
			
  }
  

  .row-offcanvas-left {
    left: 5px;
  }
 

  .sidebar-offcanvas {
    left: -95%; /* 6 columns */
  }

    .row-offcanvas-left.active .sidebar-offcanvas{
    left: 0%; /* 6 columns */
	background: #fff none repeat scroll 0 0 !important;
box-shadow: 0 0 20px grey !important;
	opacity:1;
  }

  .sidebar-offcanvas {
    position: absolute;
    top: 0;
   /* width: 65%;  6 columns */
	opacity:0;
  }
}
