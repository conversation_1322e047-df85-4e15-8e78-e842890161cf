{"version": 3, "file": "ZeroClipboard.Core.min.js", "sources": ["ZeroClipboard.Core.js"], "names": ["window", "undefined", "_zcSwfVersion", "_currentElement", "_copyTarget", "_window", "_document", "document", "_navigator", "navigator", "_setTimeout", "setTimeout", "_clearTimeout", "clearTimeout", "_setInterval", "setInterval", "_clearInterval", "clearInterval", "_getComputedStyle", "getComputedStyle", "_encodeURIComponent", "encodeURIComponent", "_ActiveXObject", "ActiveXObject", "_Error", "Error", "_parseInt", "Number", "parseInt", "_parseFloat", "parseFloat", "_isNaN", "isNaN", "_now", "Date", "now", "_keys", "Object", "keys", "_defineProperty", "defineProperty", "_hasOwn", "prototype", "hasOwnProperty", "_slice", "Array", "slice", "_unwrap", "unwrapper", "el", "wrap", "unwrap", "div", "createElement", "unwrappedDiv", "nodeType", "e", "_args", "argumentsObj", "call", "_extend", "i", "len", "arg", "prop", "src", "copy", "args", "arguments", "target", "length", "_deepCopy", "source", "_pick", "obj", "newObj", "_omit", "indexOf", "_deleteOwnProperties", "_containedBy", "ancestorEl", "ownerDocument", "parentNode", "_getDirPathOfUrl", "url", "dir", "split", "lastIndexOf", "_getCurrentScriptUrlFromErrorStack", "stack", "matches", "match", "_getCurrentScriptUrlFromError", "err", "sourceURL", "fileName", "_getCurrentScriptUrl", "jsPath", "scripts", "currentScript", "getElementsByTagName", "readyState", "_getUnanimousScriptParentDir", "jsDir", "_getDefaultSwfPath", "_pageIsFramed", "opener", "top", "parent", "_flashState", "bridge", "version", "pluginType", "disabled", "outdated", "sandboxed", "unavailable", "degraded", "deactivated", "overdue", "ready", "_minimumFlashVersion", "_handlers", "_clipData", "_clipDataFormatMap", "_flashCheckTimeout", "_swfFallbackCheckInterval", "_eventMessages", "error", "flash-disabled", "flash-outdated", "flash-sandboxed", "flash-unavailable", "flash-degraded", "flash-deactivated", "flash-overdue", "version-mismatch", "clipboard-error", "config-mismatch", "swf-not-found", "_errorsThatOnlyOccurAfterFlashLoads", "_flashStateErrorNames", "_flashStateErrorNameMatchingRegex", "RegExp", "map", "errorName", "replace", "join", "_flashStateEnabledErrorNameMatchingRegex", "_globalConfig", "swfPath", "trustedDomains", "location", "host", "cacheBust", "forceEnhancedClipboard", "flashLoadTimeout", "autoActivate", "bubbleEvents", "containerId", "containerClass", "swfObjectId", "hoverClass", "activeClass", "forceHandCursor", "title", "zIndex", "_config", "options", "test", "_isValidHtml4Id", "_state", "_detectSandbox", "browser", "flash", "zeroclipboard", "ZeroClipboard", "config", "_isFlashUnusable", "_on", "eventType", "listener", "events", "added", "toLowerCase", "on", "push", "emit", "type", "name", "jsVersion", "swfVersion", "_off", "foundIndex", "perEventHandlers", "off", "splice", "_listeners", "_emit", "event", "eventCopy", "returnVal", "tmp", "_createEvent", "_preprocessEvent", "_dispatchCallbacks", "this", "_mapClipDataToFlash", "data", "formatMap", "_create", "previousState", "isFlashUnusable", "max<PERSON><PERSON>", "_embedSwf", "_destroy", "clearData", "blur", "_unembedSwf", "_setData", "format", "dataObj", "dataFormat", "_clearData", "_getData", "_focus", "element", "_removeClass", "_addClass", "newTitle", "getAttribute", "htmlBridge", "_getHtmlBridge", "setAttribute", "useHandCursor", "_getStyle", "_setHandCursor", "_reposition", "_blur", "removeAttribute", "style", "left", "width", "height", "_activeElement", "id", "relatedTarget", "currentTarget", "timeStamp", "msg", "message", "minimumVersion", "clipboardData", "setData", "_mapClipResultsFromFlash", "_get<PERSON><PERSON><PERSON>Target", "_addMouseData", "targetEl", "relatedTargetId", "getElementById", "srcElement", "fromElement", "toElement", "pos", "_getElementPosition", "screenLeft", "screenX", "screenTop", "screenY", "scrollLeft", "body", "documentElement", "scrollTop", "pageX", "_stageX", "pageY", "_stageY", "clientX", "clientY", "moveX", "movementX", "moveY", "movementY", "x", "y", "offsetX", "offsetY", "layerX", "layerY", "_shouldPerformAsync", "_dispatchCallback", "func", "context", "async", "apply", "wildcardTypeHandlers", "specificTypeHandlers", "handlers", "concat", "originalContext", "handleEvent", "_getSandboxStatusFromErrorEvent", "isSandboxed", "sourceIsSwf", "_source", "_clearTimeoutsAndPolling", "wasDeactivated", "textContent", "htmlContent", "value", "outerHTML", "innerHTML", "innerText", "_queueEmitClipboardErrors", "_safeActiveElement", "focus", "_fireMouseEvent", "bubbles", "cancelable", "aftercopyEvent", "errors", "errorEvent", "success", "doc", "defaults", "view", "defaultView", "canBubble", "detail", "button", "which", "createEvent", "dispatchEvent", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "metaKey", "initMouseEvent", "_watchForSwfFallbackContent", "pollWait", "Math", "min", "fallbackContentId", "_isElementVisible", "_createHtmlBridge", "container", "className", "position", "_getSafeZIndex", "flashBridge", "nodeName", "allowScriptAccess", "_determineScriptAccess", "allowNetworking", "<PERSON><PERSON>s", "_vars", "swfUrl", "_cacheBust", "divToBeReplaced", "append<PERSON><PERSON><PERSON>", "tmpDiv", "usingActiveX", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "display", "removeSwfFromIE", "<PERSON><PERSON><PERSON><PERSON>", "clipData", "newClipData", "text", "html", "rtf", "clipResults", "newResults", "tmpHash", "path", "domain", "domains", "str", "trustedOriginsExpanded", "_extractDomain", "protocol", "originOrUrl", "protocolIndex", "pathIndex", "_extractAllDomains", "origins", "resultsArray", "currentDomain", "configOptions", "swfDomain", "activeElement", "c", "cl", "classNames", "classList", "add", "remove", "getPropertyValue", "getBoundingClientRect", "elRect", "pageXOffset", "pageYOffset", "leftBorderWidth", "clientLeft", "topBorderWidth", "clientTop", "leftBodyOffset", "topBodyOffset", "bodyRect", "htmlRect", "right", "bottom", "styles", "hasCssHeight", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasCssTop", "hasCssLeft", "cssKnows", "rect", "isVisible", "visibility", "enabled", "setHandCursor", "val", "doNotReassessFlashSupport", "effectiveScriptOrigin", "frame", "frameError", "frameElement", "hasAttribute", "_detectFlashSupport", "parseFlashVersion", "desc", "isPepperFlash", "flashPlayerFileName", "inspectPlugin", "plugin", "hasFlash", "flashVersion", "description", "filename", "isPPAPI", "ax", "mimeType", "isActiveX", "plugins", "mimeTypes", "enabledPlugin", "GetVariable", "e1", "e2", "e3", "_createClient", "writable", "configurable", "enumerable", "state", "create", "destroy", "getData", "activate", "deactivate", "define", "amd", "module", "exports"], "mappings": ";;;;;;;;CAQA,SAAUA,EAAQC,GAChB,YAKA,IAoSIC,GAUAC,EAKAC,EAnTAC,EAAUL,EAAQM,EAAYD,EAAQE,SAAUC,EAAaH,EAAQI,UAAWC,EAAcL,EAAQM,WAAYC,EAAgBP,EAAQQ,aAAcC,EAAeT,EAAQU,YAAaC,EAAiBX,EAAQY,cAAeC,EAAoBb,EAAQc,iBAAkBC,EAAsBf,EAAQgB,mBAAoBC,EAAiBjB,EAAQkB,cAAeC,EAASnB,EAAQoB,MAAOC,EAAYrB,EAAQsB,OAAOC,UAAYvB,EAAQuB,SAAUC,EAAcxB,EAAQsB,OAAOG,YAAczB,EAAQyB,WAAYC,EAAS1B,EAAQsB,OAAOK,OAAS3B,EAAQ2B,MAAOC,EAAO5B,EAAQ6B,KAAKC,IAAKC,EAAQ/B,EAAQgC,OAAOC,KAAMC,EAAkBlC,EAAQgC,OAAOG,eAAgBC,EAAUpC,EAAQgC,OAAOK,UAAUC,eAAgBC,EAASvC,EAAQwC,MAAMH,UAAUI,MAAOC,EAAU,WAC1vB,GAAIC,GAAY,SAASC,GACvB,MAAOA,GAET,IAA4B,kBAAjB5C,GAAQ6C,MAAiD,kBAAnB7C,GAAQ8C,OACvD,IACE,GAAIC,GAAM9C,EAAU+C,cAAc,OAC9BC,EAAejD,EAAQ8C,OAAOC,EACb,KAAjBA,EAAIG,UAAkBD,GAA0C,IAA1BA,EAAaC,WACrDP,EAAY3C,EAAQ8C,QAEtB,MAAOK,IAEX,MAAOR,MAQLS,EAAQ,SAASC,GACnB,MAAOd,GAAOe,KAAKD,EAAc,IAQ/BE,EAAU,WACZ,GAAIC,GAAGC,EAAKC,EAAKC,EAAMC,EAAKC,EAAMC,EAAOV,EAAMW,WAAYC,EAASF,EAAK,MACzE,KAAKN,EAAI,EAAGC,EAAMK,EAAKG,OAAYR,EAAJD,EAASA,IACtC,GAAuB,OAAlBE,EAAMI,EAAKN,IACd,IAAKG,IAAQD,GACPtB,EAAQkB,KAAKI,EAAKC,KACpBC,EAAMI,EAAOL,GACbE,EAAOH,EAAIC,GACPK,IAAWH,GAAQA,IAASjE,IAC9BoE,EAAOL,GAAQE,GAMzB,OAAOG,IAQLE,EAAY,SAASC,GACvB,GAAIN,GAAML,EAAGC,EAAKE,CAClB,IAAsB,gBAAXQ,IAAiC,MAAVA,GAA6C,gBAApBA,GAAOjB,SAChEW,EAAOM,MACF,IAA6B,gBAAlBA,GAAOF,OAEvB,IADAJ,KACKL,EAAI,EAAGC,EAAMU,EAAOF,OAAYR,EAAJD,EAASA,IACpCpB,EAAQkB,KAAKa,EAAQX,KACvBK,EAAKL,GAAKU,EAAUC,EAAOX,SAG1B,CACLK,IACA,KAAKF,IAAQQ,GACP/B,EAAQkB,KAAKa,EAAQR,KACvBE,EAAKF,GAAQO,EAAUC,EAAOR,KAIpC,MAAOE,IAULO,EAAQ,SAASC,EAAKpC,GAExB,IAAK,GADDqC,MACKd,EAAI,EAAGC,EAAMxB,EAAKgC,OAAYR,EAAJD,EAASA,IACtCvB,EAAKuB,IAAMa,KACbC,EAAOrC,EAAKuB,IAAMa,EAAIpC,EAAKuB,IAG/B,OAAOc,IASLC,EAAQ,SAASF,EAAKpC,GACxB,GAAIqC,KACJ,KAAK,GAAIX,KAAQU,GACY,KAAvBpC,EAAKuC,QAAQb,KACfW,EAAOX,GAAQU,EAAIV,GAGvB,OAAOW,IAQLG,EAAuB,SAASJ,GAClC,GAAIA,EACF,IAAK,GAAIV,KAAQU,GACXjC,EAAQkB,KAAKe,EAAKV,UACbU,GAAIV,EAIjB,OAAOU,IAQLK,EAAe,SAAS9B,EAAI+B,GAC9B,GAAI/B,GAAsB,IAAhBA,EAAGM,UAAkBN,EAAGgC,eAAiBD,IAAuC,IAAxBA,EAAWzB,UAAkByB,EAAWC,eAAiBD,EAAWC,gBAAkBhC,EAAGgC,eAAyC,IAAxBD,EAAWzB,WAAmByB,EAAWC,eAAiBD,IAAe/B,EAAGgC,eACtP,EAAG,CACD,GAAIhC,IAAO+B,EACT,OAAO,CAET/B,GAAKA,EAAGiC,iBACDjC,EAEX,QAAO,GAQLkC,EAAmB,SAASC,GAC9B,GAAIC,EAKJ,OAJmB,gBAARD,IAAoBA,IAC7BC,EAAMD,EAAIE,MAAM,KAAK,GAAGA,MAAM,KAAK,GACnCD,EAAMD,EAAItC,MAAM,EAAGsC,EAAIG,YAAY,KAAO,IAErCF,GAQLG,EAAqC,SAASC,GAChD,GAAIL,GAAKM,CAYT,OAXqB,gBAAVD,IAAsBA,IAC/BC,EAAUD,EAAME,MAAM,sIAClBD,GAAWA,EAAQ,GACrBN,EAAMM,EAAQ,IAEdA,EAAUD,EAAME,MAAM,kEAClBD,GAAWA,EAAQ,KACrBN,EAAMM,EAAQ,MAIbN,GAQLQ,EAAgC,WAClC,GAAIR,GAAKS,CACT,KACE,KAAM,IAAIrE,GACV,MAAOgC,GACPqC,EAAMrC,EAKR,MAHIqC,KACFT,EAAMS,EAAIC,WAAaD,EAAIE,UAAYP,EAAmCK,EAAIJ,QAEzEL,GAQLY,EAAuB,WACzB,GAAIC,GAAQC,EAASrC,CACrB,IAAIvD,EAAU6F,gBAAkBF,EAAS3F,EAAU6F,cAAclC,KAC/D,MAAOgC,EAGT,IADAC,EAAU5F,EAAU8F,qBAAqB,UAClB,IAAnBF,EAAQ5B,OACV,MAAO4B,GAAQ,GAAGjC,KAAOhE,CAE3B,IAAI,cAAgBiG,GAAQ,GAC1B,IAAKrC,EAAIqC,EAAQ5B,OAAQT,KACvB,GAA8B,gBAA1BqC,EAAQrC,GAAGwC,aAAiCJ,EAASC,EAAQrC,GAAGI,KAClE,MAAOgC,EAIb,OAA6B,YAAzB3F,EAAU+F,aAA6BJ,EAASC,EAAQA,EAAQ5B,OAAS,GAAGL,KACvEgC,GAELA,EAASL,KACJK,EAEFhG,GAULqG,EAA+B,WACjC,GAAIzC,GAAG0C,EAAON,EAAQC,EAAU5F,EAAU8F,qBAAqB,SAC/D,KAAKvC,EAAIqC,EAAQ5B,OAAQT,KAAO,CAC9B,KAAMoC,EAASC,EAAQrC,GAAGI,KAAM,CAC9BsC,EAAQ,IACR,OAGF,GADAN,EAASd,EAAiBc,GACb,MAATM,EACFA,EAAQN,MACH,IAAIM,IAAUN,EAAQ,CAC3BM,EAAQ,IACR,QAGJ,MAAOA,IAAStG,GASduG,EAAqB,WACvB,GAAID,GAAQpB,EAAiBa,MAA2BM,KAAkC,EAC1F,OAAOC,GAAQ,qBAMbE,EAAgB,WAClB,MAAwB,OAAjBzG,EAAO0G,WAAqB1G,EAAO2G,KAAO3G,GAAUA,EAAO2G,OAAS3G,EAAO4G,QAAU5G,GAAUA,EAAO4G,WAM3GC,GACFC,OAAQ,KACRC,QAAS,QACTC,WAAY,UACZC,SAAU,KACVC,SAAU,KACVC,UAAW,KACXC,YAAa,KACbC,SAAU,KACVC,YAAa,KACbC,QAAS,KACTC,MAAO,MAOLC,EAAuB,SASvBC,KAeAC,KAKAC,EAAqB,KAKrBC,EAAqB,EAKrBC,EAA4B,EAK5BC,GACFP,MAAO,qCACPQ,OACEC,iBAAkB,sHAClBC,iBAAkB,iDAClBC,kBAAmB,qEACnBC,oBAAqB,iEACrBC,iBAAkB,+EAClBC,oBAAqB,0TACrBC,gBAAiB,+EACjBC,mBAAoB,kFACpBC,kBAAmB,0GACnBC,kBAAmB,6DACnBC,gBAAiB,+HAQjBC,GAAwC,oBAAqB,iBAAkB,gBAAiB,mBAAoB,kBAAmB,mBAMvIC,GAA0B,iBAAkB,iBAAkB,kBAAmB,oBAAqB,iBAAkB,oBAAqB,iBAK7IC,EAAoC,GAAIC,QAAO,WAAaF,EAAsBG,IAAI,SAASC,GACjG,MAAOA,GAAUC,QAAQ,UAAW,MACnCC,KAAK,KAAO,MAMXC,EAA2C,GAAIL,QAAO,WAAaF,EAAsB/F,MAAM,GAAGkG,IAAI,SAASC,GACjH,MAAOA,GAAUC,QAAQ,UAAW,MACnCC,KAAK,KAAO,MAKXE,GACFC,QAAS9C,IACT+C,eAAgBvJ,EAAOwJ,SAASC,MAASzJ,EAAOwJ,SAASC,SACzDC,WAAW,EACXC,wBAAwB,EACxBC,iBAAkB,IAClBC,cAAc,EACdC,cAAc,EACdC,YAAa,mCACbC,eAAgB,iCAChBC,YAAa,oCACbC,WAAY,yBACZC,YAAa,0BACbC,iBAAiB,EACjBC,MAAO,KACPC,OAAQ,WAMNC,EAAU,SAASC,GACrB,GAAuB,gBAAZA,IAAoC,OAAZA,EACjC,IAAK,GAAIxG,KAAQwG,GACf,GAAI/H,EAAQkB,KAAK6G,EAASxG,GACxB,GAAI,kDAAkDyG,KAAKzG,GACzDqF,EAAcrF,GAAQwG,EAAQxG,OACzB,IAA0B,MAAtB6C,EAAYC,OACrB,GAAa,gBAAT9C,GAAmC,gBAATA,EAAwB,CACpD,IAAI0G,GAAgBF,EAAQxG,IAG1B,KAAM,IAAIvC,OAAM,kBAAoBuC,EAAO,8CAF3CqF,GAAcrF,GAAQwG,EAAQxG,OAKhCqF,GAAcrF,GAAQwG,EAAQxG,EAMxC,EAAA,GAAuB,gBAAZwG,KAAwBA,EAMnC,MAAOjG,GAAU8E,EALf,IAAI5G,EAAQkB,KAAK0F,EAAemB,GAC9B,MAAOnB,GAAcmB,KAUvBG,EAAS,WAEX,MADAC,OAEEC,QAASpG,EAAMjE,GAAc,YAAa,WAAY,YACtDsK,MAAOlG,EAAMiC,GAAe,WAC5BkE,eACEhE,QAASiE,GAAcjE,QACvBkE,OAAQD,GAAcC,YAQxBC,GAAmB,WACrB,SAAUrE,EAAYI,UAAYJ,EAAYK,UAAYL,EAAYM,WAAaN,EAAYO,aAAeP,EAAYQ,UAAYR,EAAYS,cAMhJ6D,GAAM,SAASC,EAAWC,GAC5B,GAAIxH,GAAGC,EAAKwH,EAAQC,IACpB,IAAyB,gBAAdH,IAA0BA,EACnCE,EAASF,EAAUI,cAAclG,MAAM,WAClC,IAAyB,gBAAd8F,IAA0BA,GAAiC,mBAAbC,GAC9D,IAAKxH,IAAKuH,GACJ3I,EAAQkB,KAAKyH,EAAWvH,IAAmB,gBAANA,IAAkBA,GAA6B,kBAAjBuH,GAAUvH,IAC/EmH,GAAcS,GAAG5H,EAAGuH,EAAUvH,GAIpC,IAAIyH,GAAUA,EAAOhH,OAAQ,CAC3B,IAAKT,EAAI,EAAGC,EAAMwH,EAAOhH,OAAYR,EAAJD,EAASA,IACxCuH,EAAYE,EAAOzH,GAAGqF,QAAQ,MAAO,IACrCqC,EAAMH,IAAa,EACd1D,EAAU0D,KACb1D,EAAU0D,OAEZ1D,EAAU0D,GAAWM,KAAKL,EAO5B,IALIE,EAAM/D,OAASX,EAAYW,OAC7BwD,GAAcW,MACZC,KAAM,UAGNL,EAAMvD,MAAO,CACf,IAAKnE,EAAI,EAAGC,EAAM+E,EAAsBvE,OAAYR,EAAJD,EAASA,IACvD,GAAIgD,EAAYgC,EAAsBhF,GAAGqF,QAAQ,UAAW,QAAS,EAAM,CACzE8B,GAAcW,MACZC,KAAM,QACNC,KAAMhD,EAAsBhF,IAE9B,OAGA3D,IAAkBD,GAAa+K,GAAcjE,UAAY7G,GAC3D8K,GAAcW,MACZC,KAAM,QACNC,KAAM,mBACNC,UAAWd,GAAcjE,QACzBgF,WAAY7L,KAKpB,MAAO8K,KAMLgB,GAAO,SAASZ,EAAWC,GAC7B,GAAIxH,GAAGC,EAAKmI,EAAYX,EAAQY,CAChC,IAAyB,IAArB9H,UAAUE,OACZgH,EAASlJ,EAAMsF,OACV,IAAyB,gBAAd0D,IAA0BA,EAC1CE,EAASF,EAAU9F,MAAM,WACpB,IAAyB,gBAAd8F,IAA0BA,GAAiC,mBAAbC,GAC9D,IAAKxH,IAAKuH,GACJ3I,EAAQkB,KAAKyH,EAAWvH,IAAmB,gBAANA,IAAkBA,GAA6B,kBAAjBuH,GAAUvH,IAC/EmH,GAAcmB,IAAItI,EAAGuH,EAAUvH,GAIrC,IAAIyH,GAAUA,EAAOhH,OACnB,IAAKT,EAAI,EAAGC,EAAMwH,EAAOhH,OAAYR,EAAJD,EAASA,IAGxC,GAFAuH,EAAYE,EAAOzH,GAAG2H,cAActC,QAAQ,MAAO,IACnDgD,EAAmBxE,EAAU0D,GACzBc,GAAoBA,EAAiB5H,OACvC,GAAI+G,EAEF,IADAY,EAAaC,EAAiBrH,QAAQwG,GAChB,KAAfY,GACLC,EAAiBE,OAAOH,EAAY,GACpCA,EAAaC,EAAiBrH,QAAQwG,EAAUY,OAGlDC,GAAiB5H,OAAS,CAKlC,OAAO0G,KAMLqB,GAAa,SAASjB,GACxB,GAAIlH,EAMJ,OAJEA,GADuB,gBAAdkH,IAA0BA,EAC5B7G,EAAUmD,EAAU0D,KAAe,KAEnC7G,EAAUmD,IAQjB4E,GAAQ,SAASC,GACnB,GAAIC,GAAWC,EAAWC,CAE1B,OADAH,GAAQI,GAAaJ,GAChBA,IAGDK,GAAiBL,GAGF,UAAfA,EAAMX,MAAoB/E,EAAYU,WAAY,EAC7CyD,GAAcW,MACnBC,KAAM,QACNC,KAAM,mBAGVW,EAAY5I,KAAY2I,GACxBM,GAAmBlJ,KAAKmJ,KAAMN,GACX,SAAfD,EAAMX,OACRc,EAAMK,GAAoBpF,GAC1B8E,EAAYC,EAAIM,KAChBpF,EAAqB8E,EAAIO,WAEpBR,GAnBP,QAyBES,GAAU,WACZ,GAAIC,GAAgBtG,EAAYM,SAKhC,IAJAyD,KACiC,iBAAtB/D,GAAYW,QACrBX,EAAYW,OAAQ,GAElBX,EAAYM,YAAcgG,GAAiBtG,EAAYM,aAAc,EACvEN,EAAYW,OAAQ,EACpBwD,GAAcW,MACZC,KAAM,QACNC,KAAM,wBAEH,KAAKb,GAAcoC,mBAA4C,OAAvBvG,EAAYC,OAAiB,CAC1E,GAAIuG,GAAUhE,EAAcO,gBACL,iBAAZyD,IAAwBA,GAAW,IAC5CxF,EAAqBnH,EAAY,WACQ,iBAA5BmG,GAAYS,cACrBT,EAAYS,aAAc,GAExBT,EAAYS,eAAgB,GAC9B0D,GAAcW,MACZC,KAAM,QACNC,KAAM,uBAGTwB,IAELxG,EAAYU,SAAU,EACtB+F,OAOAC,GAAW,WACbvC,GAAcwC,YACdxC,GAAcyC,OACdzC,GAAcW,KAAK,WACnB+B,KACA1C,GAAcmB,OAMZwB,GAAW,SAASC,EAAQZ,GAC9B,GAAIa,EACJ,IAAsB,gBAAXD,IAAuBA,GAA0B,mBAATZ,GACjDa,EAAUD,EACV5C,GAAcwC,gBACT,CAAA,GAAsB,gBAAXI,KAAuBA,EAIvC,MAHAC,MACAA,EAAQD,GAAUZ,EAIpB,IAAK,GAAIc,KAAcD,GACK,gBAAfC,IAA2BA,GAAcrL,EAAQkB,KAAKkK,EAASC,IAA8C,gBAAxBD,GAAQC,IAA4BD,EAAQC,KAC1InG,EAAUmG,GAAcD,EAAQC,KAQlCC,GAAa,SAASH,GACF,mBAAXA,IACT9I,EAAqB6C,GACrBC,EAAqB,MACM,gBAAXgG,IAAuBnL,EAAQkB,KAAKgE,EAAWiG,UACxDjG,GAAUiG,IAOjBI,GAAW,SAASJ,GACtB,MAAsB,mBAAXA,GACFrJ,EAAUoD,GACU,gBAAXiG,IAAuBnL,EAAQkB,KAAKgE,EAAWiG,GACxDjG,EAAUiG,GADZ,QAQLK,GAAS,SAASC,GACpB,GAAMA,GAAgC,IAArBA,EAAQ3K,SAAzB,CAGIpD,IACFgO,GAAahO,EAAiBkJ,EAAcc,aACxChK,IAAoB+N,GACtBC,GAAahO,EAAiBkJ,EAAca,aAGhD/J,EAAkB+N,EAClBE,GAAUF,EAAS7E,EAAca,WACjC,IAAImE,GAAWH,EAAQI,aAAa,UAAYjF,EAAcgB,KAC9D,IAAwB,gBAAbgE,IAAyBA,EAAU,CAC5C,GAAIE,GAAaC,GAAe3H,EAAYC,OACxCyH,IACFA,EAAWE,aAAa,QAASJ,GAGrC,GAAIK,GAAgBrF,EAAce,mBAAoB,GAAyC,YAAjCuE,GAAUT,EAAS,SACjFU,IAAeF,GACfG,OAMEC,GAAQ,WACV,GAAIP,GAAaC,GAAe3H,EAAYC,OACxCyH,KACFA,EAAWQ,gBAAgB,SAC3BR,EAAWS,MAAMC,KAAO,MACxBV,EAAWS,MAAMrI,IAAM,UACvB4H,EAAWS,MAAME,MAAQ,MACzBX,EAAWS,MAAMG,OAAS,OAExBhP,IACFgO,GAAahO,EAAiBkJ,EAAca,YAC5CiE,GAAahO,EAAiBkJ,EAAcc,aAC5ChK,EAAkB,OAOlBiP,GAAiB,WACnB,MAAOjP,IAAmB,MAMxBuK,GAAkB,SAAS2E,GAC7B,MAAqB,gBAAPA,IAAmBA,GAAM,+BAA+B5E,KAAK4E,IAMzE1C,GAAe,SAASJ,GAC1B,GAAInB,EAOJ,IANqB,gBAAVmB,IAAsBA,GAC/BnB,EAAYmB,EACZA,MAC0B,gBAAVA,IAAsBA,GAA+B,gBAAfA,GAAMX,MAAqBW,EAAMX,OACvFR,EAAYmB,EAAMX,MAEfR,EAAL,CAGAA,EAAYA,EAAUI,eACjBe,EAAMlI,SAAW,4BAA4BoG,KAAKW,IAA4B,UAAdA,GAAwC,oBAAfmB,EAAMV,QAClGU,EAAMlI,OAASjE,GAEjBwD,EAAQ2I,GACNX,KAAMR,EACN/G,OAAQkI,EAAMlI,QAAUlE,GAAmB,KAC3CmP,cAAe/C,EAAM+C,eAAiB,KACtCC,cAAe1I,GAAeA,EAAYC,QAAU,KACpD0I,UAAWjD,EAAMiD,WAAavN,KAAU,MAE1C,IAAIwN,GAAM1H,EAAewE,EAAMX,KAsC/B,OArCmB,UAAfW,EAAMX,MAAoBW,EAAMV,MAAQ4D,IAC1CA,EAAMA,EAAIlD,EAAMV,OAEd4D,IACFlD,EAAMmD,QAAUD,GAEC,UAAflD,EAAMX,MACRhI,EAAQ2I,GACNlI,OAAQ,KACR0C,QAASF,EAAYE,UAGN,UAAfwF,EAAMX,OACJ9C,EAAkC2B,KAAK8B,EAAMV,OAC/CjI,EAAQ2I,GACNlI,OAAQ,KACRsL,eAAgBlI,IAGhB2B,EAAyCqB,KAAK8B,EAAMV,OACtDjI,EAAQ2I,GACNxF,QAASF,EAAYE,WAIR,SAAfwF,EAAMX,OACRW,EAAMqD,eACJC,QAAS7E,GAAc6E,QACvBrC,UAAWxC,GAAcwC,YAGV,cAAfjB,EAAMX,OACRW,EAAQuD,GAAyBvD,EAAO3E,IAEtC2E,EAAMlI,SAAWkI,EAAM+C,gBACzB/C,EAAM+C,cAAgBS,GAAkBxD,EAAMlI,SAEzC2L,GAAczD,KAMnBwD,GAAoB,SAASE,GAC/B,GAAIC,GAAkBD,GAAYA,EAAS3B,cAAgB2B,EAAS3B,aAAa,wBACjF,OAAO4B,GAAkB5P,EAAU6P,eAAeD,GAAmB,MAMnEF,GAAgB,SAASzD,GAC3B,GAAIA,GAAS,8CAA8C9B,KAAK8B,EAAMX,MAAO,CAC3E,GAAIwE,GAAa7D,EAAMlI,OACnBgM,EAA6B,eAAf9D,EAAMX,MAAyBW,EAAM+C,cAAgB/C,EAAM+C,cAAgBrP,EACzFqQ,EAA2B,cAAf/D,EAAMX,MAAwBW,EAAM+C,cAAgB/C,EAAM+C,cAAgBrP,EACtFsQ,EAAMC,GAAoBJ,GAC1BK,EAAapQ,EAAQoQ,YAAcpQ,EAAQqQ,SAAW,EACtDC,EAAYtQ,EAAQsQ,WAAatQ,EAAQuQ,SAAW,EACpDC,EAAavQ,EAAUwQ,KAAKD,WAAavQ,EAAUyQ,gBAAgBF,WACnEG,EAAY1Q,EAAUwQ,KAAKE,UAAY1Q,EAAUyQ,gBAAgBC,UACjEC,EAAQV,EAAItB,MAAiC,gBAAlB1C,GAAM2E,QAAuB3E,EAAM2E,QAAU,GACxEC,EAAQZ,EAAI5J,KAAgC,gBAAlB4F,GAAM6E,QAAuB7E,EAAM6E,QAAU,GACvEC,EAAUJ,EAAQJ,EAClBS,EAAUH,EAAQH,EAClBN,EAAUD,EAAaY,EACvBT,EAAUD,EAAYW,EACtBC,EAAmC,gBAApBhF,GAAMiF,UAAyBjF,EAAMiF,UAAY,EAChEC,EAAmC,gBAApBlF,GAAMmF,UAAyBnF,EAAMmF,UAAY,QAC7DnF,GAAM2E,cACN3E,GAAM6E,QACbxN,EAAQ2I,GACN6D,WAAYA,EACZC,YAAaA,EACbC,UAAWA,EACXI,QAASA,EACTE,QAASA,EACTK,MAAOA,EACPE,MAAOA,EACPE,QAASA,EACTC,QAASA,EACTK,EAAGN,EACHO,EAAGN,EACHE,UAAWD,EACXG,UAAWD,EACXI,QAAS,EACTC,QAAS,EACTC,OAAQ,EACRC,OAAQ,IAGZ,MAAOzF,IAQL0F,GAAsB,SAAS1F,GACjC,GAAInB,GAAYmB,GAA+B,gBAAfA,GAAMX,MAAqBW,EAAMX,MAAQ,EACzE,QAAQ,gCAAgCnB,KAAKW,IAQ3C8G,GAAoB,SAASC,EAAMC,EAASjO,EAAMkO,GAChDA,EACF3R,EAAY,WACVyR,EAAKG,MAAMF,EAASjO,IACnB,GAEHgO,EAAKG,MAAMF,EAASjO,IASpB0I,GAAqB,SAASN,GAChC,GAAuB,gBAAVA,IAAsBA,GAASA,EAAMX,KAAlD,CAGA,GAAIyG,GAAQJ,GAAoB1F,GAC5BgG,EAAuB7K,EAAU,SACjC8K,EAAuB9K,EAAU6E,EAAMX,UACvC6G,EAAWF,EAAqBG,OAAOF,EAC3C,IAAIC,GAAYA,EAASnO,OAAQ,CAC/B,GAAIT,GAAGC,EAAKqO,EAAMC,EAAS5F,EAAWmG,EAAkB7F,IACxD,KAAKjJ,EAAI,EAAGC,EAAM2O,EAASnO,OAAYR,EAAJD,EAASA,IAC1CsO,EAAOM,EAAS5O,GAChBuO,EAAUO,EACU,gBAATR,IAA8C,kBAAlB9R,GAAQ8R,KAC7CA,EAAO9R,EAAQ8R,IAEG,gBAATA,IAAqBA,GAAoC,kBAArBA,GAAKS,cAClDR,EAAUD,EACVA,EAAOA,EAAKS,aAEM,kBAATT,KACT3F,EAAY5I,KAAY2I,GACxB2F,GAAkBC,EAAMC,GAAW5F,GAAa6F,IAItD,MAAOvF,QAOL+F,GAAkC,SAAStG,GAC7C,GAAIuG,GAAc,IAIlB,QAHIrM,KAAkB,GAAS8F,GAAwB,UAAfA,EAAMX,MAAoBW,EAAMV,MAAoE,KAA5DjD,EAAoC/D,QAAQ0H,EAAMV,SAChIiH,GAAc,GAETA,GAOLlG,GAAmB,SAASL,GAC9B,GAAI2B,GAAU3B,EAAMlI,QAAUlE,GAAmB,KAC7C4S,EAAgC,QAAlBxG,EAAMyG,OAExB,cADOzG,GAAMyG,QACLzG,EAAMX,MACb,IAAK,QACJ,GAAIkH,GAA6B,oBAAfvG,EAAMV,MAA8BgH,GAAgCtG,EAC3D,kBAAhBuG,KACTjM,EAAYM,UAAY2L,GAEwB,KAA9CjK,EAAsBhE,QAAQ0H,EAAMV,MACtCjI,EAAQiD,GACNI,SAAyB,mBAAfsF,EAAMV,KAChB3E,SAAyB,mBAAfqF,EAAMV,KAChBzE,YAA4B,sBAAfmF,EAAMV,KACnBxE,SAAyB,mBAAfkF,EAAMV,KAChBvE,YAA4B,sBAAfiF,EAAMV,KACnBtE,QAAwB,kBAAfgF,EAAMV,KACfrE,OAAO,IAEe,qBAAf+E,EAAMV,OACf3L,EAAgBqM,EAAMR,WACtBnI,EAAQiD,GACNI,UAAU,EACVC,UAAU,EACVE,aAAa,EACbC,UAAU,EACVC,aAAa,EACbC,SAAS,EACTC,OAAO,KAGXyL,IACA,MAED,KAAK,QACJ/S,EAAgBqM,EAAMR,UACtB,IAAImH,GAAiBrM,EAAYS,eAAgB,CACjD1D,GAAQiD,GACNI,UAAU,EACVC,UAAU,EACVC,WAAW,EACXC,aAAa,EACbC,UAAU,EACVC,aAAa,EACbC,QAAS2L,EACT1L,OAAQ0L,IAEVD,IACA,MAED,KAAK,aACJ7S,EAAc8N,CACd,MAED,KAAK,OACJ,GAAIiF,GAAaC,EAAanD,EAAW1D,EAAM+C,eACzC3H,EAAU,eAAgBA,EAAU,eAAkBsI,IAAamD,EAAcnD,EAASoD,OAASpD,EAASqD,WAAarD,EAASsD,aAAeJ,EAAclD,EAASoD,OAASpD,EAASkD,aAAelD,EAASuD,YACtNjH,EAAMqD,cAAcpC,YACpBjB,EAAMqD,cAAcC,QAAQ,aAAcsD,GACtCC,IAAgBD,GAClB5G,EAAMqD,cAAcC,QAAQ,YAAauD,KAEjCzL,EAAU,eAAiB4E,EAAMlI,SAAW8O,EAAc5G,EAAMlI,OAAOiK,aAAa,0BAC9F/B,EAAMqD,cAAcpC,YACpBjB,EAAMqD,cAAcC,QAAQ,aAAcsD,GAE5C,MAED,KAAK,YACJM,GAA0BlH,GAC1BvB,GAAcwC,YACVU,GAAWA,IAAYwF,MAAwBxF,EAAQyF,OACzDzF,EAAQyF,OAEV,MAED,KAAK,aACJ3I,GAAc2I,MAAMzF,GAChB7E,EAAcS,gBAAiB,GAAQiJ,IACrC7E,GAAWA,IAAY3B,EAAM+C,gBAAkBvK,EAAawH,EAAM+C,cAAepB,IACnF0F,GAAgBhQ,KAAY2I,GAC1BX,KAAM,aACNiI,SAAS,EACTC,YAAY,KAGhBF,GAAgBhQ,KAAY2I,GAC1BX,KAAM,eAGV,MAED,KAAK,YACJZ,GAAcyC,OACVpE,EAAcS,gBAAiB,GAAQiJ,IACrC7E,GAAWA,IAAY3B,EAAM+C,gBAAkBvK,EAAawH,EAAM+C,cAAepB,IACnF0F,GAAgBhQ,KAAY2I,GAC1BX,KAAM,aACNiI,SAAS,EACTC,YAAY,KAGhBF,GAAgBhQ,KAAY2I,GAC1BX,KAAM,cAGV,MAED,KAAK,aACJwC,GAAUF,EAAS7E,EAAcc,aAC7Bd,EAAcS,gBAAiB,GAAQiJ,GACzCa,GAAgBhQ,KAAY2I,GAC1BX,KAAMW,EAAMX,KAAK9I,MAAM,KAG3B,MAED,KAAK,WACJqL,GAAaD,EAAS7E,EAAcc,aAChCd,EAAcS,gBAAiB,GAAQiJ,GACzCa,GAAgBhQ,KAAY2I,GAC1BX,KAAMW,EAAMX,KAAK9I,MAAM,KAG3B,MAED,KAAK,SACJ1C,EAAc,KACViJ,EAAcS,gBAAiB,GAAQiJ,GACzCa,GAAgBhQ,KAAY2I,GAC1BX,KAAMW,EAAMX,KAAK9I,MAAM,KAG3B,MAED,KAAK,aACAuG,EAAcS,gBAAiB,GAAQiJ,GACzCa,GAAgBhQ,KAAY2I,GAC1BX,KAAMW,EAAMX,KAAK9I,MAAM,MAK7B,MAAI,8CAA8C2H,KAAK8B,EAAMX,OACpD,EADT,QAQE6H,GAA4B,SAASM,GACvC,GAAIA,EAAeC,QAAUD,EAAeC,OAAO1P,OAAS,EAAG,CAC7D,GAAI2P,GAAa1P,EAAUwP,EAC3BnQ,GAAQqQ,GACNrI,KAAM,QACNC,KAAM,0BAEDoI,GAAWC,QAClBxT,EAAY,WACVsK,GAAcW,KAAKsI,IAClB,KASHL,GAAkB,SAASrH,GAC7B,GAAMA,GAA+B,gBAAfA,GAAMX,MAAqBW,EAAjD,CAGA,GAAI/I,GAAGa,EAASkI,EAAMlI,QAAU,KAAM8P,EAAM9P,GAAUA,EAAOY,eAAiB3E,EAAW8T,GACvFC,KAAMF,EAAIG,aAAejU,EACzBkU,WAAW,EACXT,YAAY,EACZU,OAAuB,UAAfjI,EAAMX,KAAmB,EAAI,EACrC6I,OAA+B,gBAAhBlI,GAAMmI,MAAqBnI,EAAMmI,MAAQ,EAA4B,gBAAjBnI,GAAMkI,OAAsBlI,EAAMkI,OAASN,EAAIQ,YAAc,EAAI,GACnIxQ,EAAOP,EAAQwQ,EAAU7H,EACvBlI,IAGD8P,EAAIQ,aAAetQ,EAAOuQ,gBAC5BzQ,GAASA,EAAKyH,KAAMzH,EAAKoQ,UAAWpQ,EAAK2P,WAAY3P,EAAKkQ,KAAMlQ,EAAKqQ,OAAQrQ,EAAKuM,QAASvM,EAAKyM,QAASzM,EAAKkN,QAASlN,EAAKmN,QAASnN,EAAK0Q,QAAS1Q,EAAK2Q,OAAQ3Q,EAAK4Q,SAAU5Q,EAAK6Q,QAAS7Q,EAAKsQ,OAAQtQ,EAAKmL,eAC/M9L,EAAI2Q,EAAIQ,YAAY,eAChBnR,EAAEyR,iBACJzR,EAAEyR,eAAe3C,MAAM9O,EAAGW,GAC1BX,EAAEwP,QAAU,KACZ3O,EAAOuQ,cAAcpR,OAoBvB0R,GAA8B,WAChC,GAAI7H,GAAUhE,EAAcO,gBAC5B,IAAuB,gBAAZyD,IAAwBA,GAAW,EAAG,CAC/C,GAAI8H,GAAWC,KAAKC,IAAI,IAAKhI,EAAU,IACnCiI,EAAoBjM,EAAcY,YAAc,kBACpDnC,GAA4BhH,EAAa,WACvC,GAAImC,GAAK3C,EAAU6P,eAAemF,EAC9BC,IAAkBtS,KACpBgQ,KACApM,EAAYS,YAAc,KAC1B0D,GAAcW,MACZC,KAAM,QACNC,KAAM,oBAGTsJ,KAOHK,GAAoB,WACtB,GAAIC,GAAYnV,EAAU+C,cAAc,MASxC,OARAoS,GAAUpG,GAAKhG,EAAcU,YAC7B0L,EAAUC,UAAYrM,EAAcW,eACpCyL,EAAUzG,MAAM2G,SAAW,WAC3BF,EAAUzG,MAAMC,KAAO,MACvBwG,EAAUzG,MAAMrI,IAAM,UACtB8O,EAAUzG,MAAME,MAAQ,MACxBuG,EAAUzG,MAAMG,OAAS,MACzBsG,EAAUzG,MAAM1E,OAAS,GAAKsL,GAAevM,EAAciB,QACpDmL,GAMLjH,GAAiB,SAASqH,GAE5B,IADA,GAAItH,GAAasH,GAAeA,EAAY3Q,WACrCqJ,GAAsC,WAAxBA,EAAWuH,UAAyBvH,EAAWrJ,YAClEqJ,EAAaA,EAAWrJ,UAE1B,OAAOqJ,IAAc,MAQnBjB,GAAY,WACd,GAAIxJ,GAAK+R,EAAchP,EAAYC,OAAQ2O,EAAYjH,GAAeqH,EACtE,KAAKA,EAAa,CAChB,GAAIE,GAAoBC,GAAuB3V,EAAQmJ,SAASC,KAAMJ,GAClE4M,EAAwC,UAAtBF,EAAgC,OAAS,MAC3DG,EAAYC,GAAMvS,GACpBkI,UAAWd,GAAcjE,SACxBsC,IACC+M,EAAS/M,EAAcC,QAAU+M,GAAWhN,EAAcC,QAASD,EACvEoM,GAAYD,IACZ,IAAIc,GAAkBhW,EAAU+C,cAAc,MAC9CoS,GAAUc,YAAYD,GACtBhW,EAAUwQ,KAAKyF,YAAYd,EAC3B,IAAIe,GAASlW,EAAU+C,cAAc,OACjCoT,EAA0C,YAA3B5P,EAAYG,UAC/BwP,GAAOjD,UAAY,eAAiBlK,EAAcY,YAAc,WAAaZ,EAAcY,YAAc,iCAAwCwM,EAAe,uDAAyD,8CAAgDL,EAAS,KAAO,KAAOK,EAAe,8BAAgCL,EAAS,MAAQ,IAAM,0CAA4CL,EAAoB,2CAAkDE,EAAkB,gHAAiIC,EAAY,eAAsB7M,EAAcY,YAAc,0CACzqB4L,EAAcW,EAAOE,WACrBF,EAAS,KACTzT,EAAQ8S,GAAa7K,cAAgBA,GACrCyK,EAAUkB,aAAad,EAAaS,GACpCpB,KAYF,MAVKW,KACHA,EAAcvV,EAAU+I,EAAcY,aAClC4L,IAAgB/R,EAAM+R,EAAYvR,UACpCuR,EAAcA,EAAY/R,EAAM,KAE7B+R,GAAeJ,IAClBI,EAAcJ,EAAUiB,aAG5B7P,EAAYC,OAAS+O,GAAe,KAC7BA,GAMLnI,GAAc,WAChB,GAAImI,GAAchP,EAAYC,MAC9B,IAAI+O,EAAa,CACf,GAAItH,GAAaC,GAAeqH,EAC5BtH,KAC6B,YAA3B1H,EAAYG,YAA4B,cAAgB6O,IAC1DA,EAAY7G,MAAM4H,QAAU,OAC5B,QAAUC,KACR,GAA+B,IAA3BhB,EAAYxP,WAAkB,CAChC,IAAK,GAAIrC,KAAQ6R,GACkB,kBAAtBA,GAAY7R,KACrB6R,EAAY7R,GAAQ,KAGpB6R,GAAY3Q,YACd2Q,EAAY3Q,WAAW4R,YAAYjB,GAEjCtH,EAAWrJ,YACbqJ,EAAWrJ,WAAW4R,YAAYvI,OAGpC7N,GAAYmW,EAAiB,SAI7BhB,EAAY3Q,YACd2Q,EAAY3Q,WAAW4R,YAAYjB,GAEjCtH,EAAWrJ,YACbqJ,EAAWrJ,WAAW4R,YAAYvI,KAIxC0E,KACApM,EAAYW,MAAQ,KACpBX,EAAYC,OAAS,KACrBD,EAAYS,YAAc,KAC1BpH,EAAgBD,IAShB8M,GAAsB,SAASgK,GACjC,GAAIC,MAAkB/J,IACtB,IAA0B,gBAAb8J,IAAyBA,EAAtC,CAGA,IAAK,GAAIjJ,KAAciJ,GACrB,GAAIjJ,GAAcrL,EAAQkB,KAAKoT,EAAUjJ,IAA+C,gBAAzBiJ,GAASjJ,IAA4BiJ,EAASjJ,GAC3G,OAAQA,EAAWtC,eAClB,IAAK,aACL,IAAK,OACL,IAAK,WACL,IAAK,aACJwL,EAAYC,KAAOF,EAASjJ,GAC5Bb,EAAUgK,KAAOnJ,CACjB,MAED,KAAK,YACL,IAAK,OACL,IAAK,WACL,IAAK,aACJkJ,EAAYE,KAAOH,EAASjJ,GAC5Bb,EAAUiK,KAAOpJ,CACjB,MAED,KAAK,kBACL,IAAK,WACL,IAAK,MACL,IAAK,WACL,IAAK,UACL,IAAK,YACJkJ,EAAYG,IAAMJ,EAASjJ,GAC3Bb,EAAUkK,IAAMrJ,EAQtB,OACEd,KAAMgK,EACN/J,UAAWA,KASX6C,GAA2B,SAASsH,EAAanK,GACnD,GAA6B,gBAAhBmK,KAA4BA,GAAoC,gBAAdnK,KAA0BA,EACvF,MAAOmK,EAET,IAAIC,KACJ,KAAK,GAAIrT,KAAQoT,GACf,GAAI3U,EAAQkB,KAAKyT,EAAapT,GAC5B,GAAa,WAATA,EAAmB,CACrBqT,EAAWrT,GAAQoT,EAAYpT,GAAQoT,EAAYpT,GAAMlB,UACzD,KAAK,GAAIe,GAAI,EAAGC,EAAMuT,EAAWrT,GAAMM,OAAYR,EAAJD,EAASA,IACtDwT,EAAWrT,GAAMH,GAAG+J,OAASX,EAAUoK,EAAWrT,GAAMH,GAAG+J,YAExD,IAAa,YAAT5J,GAA+B,SAATA,EAC/BqT,EAAWrT,GAAQoT,EAAYpT,OAC1B,CACLqT,EAAWrT,KACX,IAAIsT,GAAUF,EAAYpT,EAC1B,KAAK,GAAI8J,KAAcwJ,GACjBxJ,GAAcrL,EAAQkB,KAAK2T,EAASxJ,IAAerL,EAAQkB,KAAKsJ,EAAWa,KAC7EuJ,EAAWrT,GAAMiJ,EAAUa,IAAewJ,EAAQxJ,IAM5D,MAAOuJ,IAULhB,GAAa,SAASkB,EAAM/M,GAC9B,GAAId,GAAuB,MAAXc,GAAmBA,GAAWA,EAAQd,aAAc,CACpE,OAAIA,IAC4B,KAAtB6N,EAAK1S,QAAQ,KAAc,IAAM,KAAO,WAAa5C,IAEtD,IAUPkU,GAAQ,SAAS3L,GACnB,GAAI3G,GAAGC,EAAK0T,EAAQC,EAASC,EAAM,GAAIC,IAQvC,IAPInN,EAAQjB,iBAC4B,gBAA3BiB,GAAQjB,eACjBkO,GAAYjN,EAAQjB,gBACuB,gBAA3BiB,GAAQjB,gBAA+B,UAAYiB,GAAQjB,iBAC3EkO,EAAUjN,EAAQjB,iBAGlBkO,GAAWA,EAAQnT,OACrB,IAAKT,EAAI,EAAGC,EAAM2T,EAAQnT,OAAYR,EAAJD,EAASA,IACzC,GAAIpB,EAAQkB,KAAK8T,EAAS5T,IAAM4T,EAAQ5T,IAA4B,gBAAf4T,GAAQ5T,GAAiB,CAE5E,GADA2T,EAASI,GAAeH,EAAQ5T,KAC3B2T,EACH,QAEF,IAAe,MAAXA,EAAgB,CAClBG,EAAuBrT,OAAS,EAChCqT,EAAuBjM,KAAK8L,EAC5B,OAEFG,EAAuBjM,KAAK4G,MAAMqF,GAA0BH,EAAQ,KAAOA,EAAQnX,EAAQmJ,SAASqO,SAAW,KAAOL,IAgB5H,MAZIG,GAAuBrT,SACzBoT,GAAO,kBAAoBtW,EAAoBuW,EAAuBxO,KAAK,OAEzEqB,EAAQb,0BAA2B,IACrC+N,IAAQA,EAAM,IAAM,IAAM,+BAEO,gBAAxBlN,GAAQP,aAA4BO,EAAQP,cACrDyN,IAAQA,EAAM,IAAM,IAAM,eAAiBtW,EAAoBoJ,EAAQP,cAExC,gBAAtBO,GAAQsB,WAA0BtB,EAAQsB,YACnD4L,IAAQA,EAAM,IAAM,IAAM,aAAetW,EAAoBoJ,EAAQsB,YAEhE4L,GASLE,GAAiB,SAASE,GAC5B,GAAmB,MAAfA,GAAuC,KAAhBA,EACzB,MAAO,KAGT,IADAA,EAAcA,EAAY5O,QAAQ,aAAc,IAC5B,KAAhB4O,EACF,MAAO,KAET,IAAIC,GAAgBD,EAAYjT,QAAQ,KACxCiT,GAAgC,KAAlBC,EAAuBD,EAAcA,EAAYhV,MAAMiV,EAAgB,EACrF,IAAIC,GAAYF,EAAYjT,QAAQ,IAEpC,OADAiT,GAA4B,KAAdE,EAAmBF,EAAgC,KAAlBC,GAAsC,IAAdC,EAAkB,KAAOF,EAAYhV,MAAM,EAAGkV,GACjHF,GAAuD,SAAxCA,EAAYhV,MAAM,IAAI0I,cAChC,KAEFsM,GAAe,MAQpB9B,GAAyB,WAC3B,GAAIiC,GAAqB,SAASC,GAChC,GAAIrU,GAAGC,EAAK4I,EAAKyL,IAIjB,IAHuB,gBAAZD,KACTA,GAAYA,IAEW,gBAAZA,KAAwBA,GAAqC,gBAAnBA,GAAQ5T,OAC7D,MAAO6T,EAET,KAAKtU,EAAI,EAAGC,EAAMoU,EAAQ5T,OAAYR,EAAJD,EAASA,IACzC,GAAIpB,EAAQkB,KAAKuU,EAASrU,KAAO6I,EAAMkL,GAAeM,EAAQrU,KAAM,CAClE,GAAY,MAAR6I,EAAa,CACfyL,EAAa7T,OAAS,EACtB6T,EAAazM,KAAK,IAClB,OAEgC,KAA9ByM,EAAatT,QAAQ6H,IACvByL,EAAazM,KAAKgB,GAIxB,MAAOyL,GAET,OAAO,UAASC,EAAeC,GAC7B,GAAIC,GAAYV,GAAeS,EAAc/O,QAC3B,QAAdgP,IACFA,EAAYF,EAEd,IAAI7O,GAAiB0O,EAAmBI,EAAc9O,gBAClDzF,EAAMyF,EAAejF,MACzB,IAAIR,EAAM,EAAG,CACX,GAAY,IAARA,GAAmC,MAAtByF,EAAe,GAC9B,MAAO,QAET,IAA8C,KAA1CA,EAAe1E,QAAQuT,GACzB,MAAY,KAARtU,GAAasU,IAAkBE,EAC1B,aAEF,SAGX,MAAO,YASP5E,GAAqB,WACvB,IACE,MAAOpT,GAAUiY,cACjB,MAAO1S,GACP,MAAO,QASPuI,GAAY,SAASF,EAASmF,GAChC,GAAImF,GAAGC,EAAI/C,EAAWgD,IAItB,IAHqB,gBAAVrF,IAAsBA,IAC/BqF,EAAarF,EAAM/N,MAAM,QAEvB4I,GAAgC,IAArBA,EAAQ3K,UAAkBmV,EAAWpU,OAAS,EAC3D,GAAI4J,EAAQyK,UACV,IAAKH,EAAI,EAAGC,EAAKC,EAAWpU,OAAYmU,EAAJD,EAAQA,IAC1CtK,EAAQyK,UAAUC,IAAIF,EAAWF,QAE9B,IAAItK,EAAQvL,eAAe,aAAc,CAE9C,IADA+S,EAAY,IAAMxH,EAAQwH,UAAY,IACjC8C,EAAI,EAAGC,EAAKC,EAAWpU,OAAYmU,EAAJD,EAAQA,IACW,KAAjD9C,EAAU7Q,QAAQ,IAAM6T,EAAWF,GAAK,OAC1C9C,GAAagD,EAAWF,GAAK,IAGjCtK,GAAQwH,UAAYA,EAAUxM,QAAQ,aAAc,IAGxD,MAAOgF,IAQLC,GAAe,SAASD,EAASmF,GACnC,GAAImF,GAAGC,EAAI/C,EAAWgD,IAItB,IAHqB,gBAAVrF,IAAsBA,IAC/BqF,EAAarF,EAAM/N,MAAM,QAEvB4I,GAAgC,IAArBA,EAAQ3K,UAAkBmV,EAAWpU,OAAS,EAC3D,GAAI4J,EAAQyK,WAAazK,EAAQyK,UAAUrU,OAAS,EAClD,IAAKkU,EAAI,EAAGC,EAAKC,EAAWpU,OAAYmU,EAAJD,EAAQA,IAC1CtK,EAAQyK,UAAUE,OAAOH,EAAWF,QAEjC,IAAItK,EAAQwH,UAAW,CAE5B,IADAA,GAAa,IAAMxH,EAAQwH,UAAY,KAAKxM,QAAQ,YAAa,KAC5DsP,EAAI,EAAGC,EAAKC,EAAWpU,OAAYmU,EAAJD,EAAQA,IAC1C9C,EAAYA,EAAUxM,QAAQ,IAAMwP,EAAWF,GAAK,IAAK,IAE3DtK,GAAQwH,UAAYA,EAAUxM,QAAQ,aAAc,IAGxD,MAAOgF,IAULS,GAAY,SAAS1L,EAAIe,GAC3B,GAAIqP,GAAQnS,EAAkB+B,EAAI,MAAM6V,iBAAiB9U,EACzD,OAAa,WAATA,GACGqP,GAAmB,SAAVA,GACQ,MAAhBpQ,EAAG6S,SAKJzC,EAJM,WAYX7C,GAAsB,SAASvN,GACjC,GAAIsN,IACFtB,KAAM,EACNtI,IAAK,EACLuI,MAAO,EACPC,OAAQ,EAEV,IAAIlM,EAAG8V,sBAAuB,CAC5B,GAAIC,GAAS/V,EAAG8V,wBACZE,EAAc5Y,EAAQ4Y,YACtBC,EAAc7Y,EAAQ6Y,YACtBC,EAAkB7Y,EAAUyQ,gBAAgBqI,YAAc,EAC1DC,EAAiB/Y,EAAUyQ,gBAAgBuI,WAAa,EACxDC,EAAiB,EACjBC,EAAgB,CACpB,IAA8C,aAA1C7K,GAAUrO,EAAUwQ,KAAM,YAA4B,CACxD,GAAI2I,GAAWnZ,EAAUwQ,KAAKiI,wBAC1BW,EAAWpZ,EAAUyQ,gBAAgBgI,uBACzCQ,GAAiBE,EAASxK,KAAOyK,EAASzK,MAAQ,EAClDuK,EAAgBC,EAAS9S,IAAM+S,EAAS/S,KAAO,EAEjD4J,EAAItB,KAAO+J,EAAO/J,KAAOgK,EAAcE,EAAkBI,EACzDhJ,EAAI5J,IAAMqS,EAAOrS,IAAMuS,EAAcG,EAAiBG,EACtDjJ,EAAIrB,MAAQ,SAAW8J,GAASA,EAAO9J,MAAQ8J,EAAOW,MAAQX,EAAO/J,KACrEsB,EAAIpB,OAAS,UAAY6J,GAASA,EAAO7J,OAAS6J,EAAOY,OAASZ,EAAOrS,IAE3E,MAAO4J,IAQLgF,GAAoB,SAAStS,GAC/B,IAAKA,EACH,OAAO,CAET,IAAI4W,GAAS3Y,EAAkB+B,EAAI,MAC/B6W,EAAejY,EAAYgY,EAAO1K,QAAU,EAC5C4K,EAAclY,EAAYgY,EAAO3K,OAAS,EAC1C8K,EAAYnY,EAAYgY,EAAOlT,MAAQ,EACvCsT,EAAapY,EAAYgY,EAAO5K,OAAS,EACzCiL,EAAWJ,GAAgBC,GAAeC,GAAaC,EACvDE,EAAOD,EAAW,KAAO1J,GAAoBvN,GAC7CmX,EAA+B,SAAnBP,EAAOjD,SAA4C,aAAtBiD,EAAOQ,aAA8BH,KAAcC,IAASL,GAAgBK,EAAKhL,OAAS,KAAO4K,GAAeI,EAAKjL,MAAQ,KAAO8K,GAAaG,EAAKxT,KAAO,KAAOsT,GAAcE,EAAKlL,MAAQ,GAC5O,OAAOmL,IAQLnH,GAA2B,WAC7BrS,EAAciH,GACdA,EAAqB,EACrB7G,EAAe8G,GACfA,EAA4B,GAQ1B+G,GAAc,WAChB,GAAIN,EACJ,IAAIpO,IAAoBoO,EAAaC,GAAe3H,EAAYC,SAAU,CACxE,GAAIyJ,GAAMC,GAAoBrQ,EAC9ByD,GAAQ2K,EAAWS,OACjBE,MAAOqB,EAAIrB,MAAQ,KACnBC,OAAQoB,EAAIpB,OAAS,KACrBxI,IAAK4J,EAAI5J,IAAM,KACfsI,KAAMsB,EAAItB,KAAO,KACjB3E,OAAQ,GAAKsL,GAAevM,EAAciB,YAU5CsE,GAAiB,SAAS0L,GACxBzT,EAAYW,SAAU,IACpBX,EAAYC,QAAsD,kBAArCD,GAAYC,OAAOyT,cAClD1T,EAAYC,OAAOyT,cAAcD,GAEjCzT,EAAYW,OAAQ,IAUtBoO,GAAiB,SAAS4E,GAC5B,GAAI,qBAAqB/P,KAAK+P,GAC5B,MAAOA,EAET,IAAIlQ,EAMJ,OALmB,gBAARkQ,IAAqBzY,EAAOyY,GAEb,gBAARA,KAChBlQ,EAASsL,GAAelU,EAAU8Y,EAAK,MAFvClQ,EAASkQ,EAIc,gBAAXlQ,GAAsBA,EAAS,QAa3CM,GAAiB,SAAS6P,GAC5B,GAAIC,GAAuBC,EAAOC,EAAYzN,EAAgBtG,EAAYM,UAAW2L,EAAc,IAEnG,IADA2H,EAA4BA,KAA8B,EACtDhU,KAAkB,EACpBqM,GAAc,MACT,CACL,IACE6H,EAAQ3a,EAAO6a,cAAgB,KAC/B,MAAOrX,GACPoX,GACE/O,KAAMrI,EAAEqI,KACR6D,QAASlM,EAAEkM,SAGf,GAAIiL,GAA4B,IAAnBA,EAAMpX,UAAqC,WAAnBoX,EAAM7E,SACzC,IACEhD,EAAc6H,EAAMG,aAAa,WACjC,MAAOtX,GACPsP,EAAc,SAEX,CACL,IACE4H,EAAwBna,SAASiX,QAAU,KAC3C,MAAOhU,GACPkX,EAAwB,MAEI,OAA1BA,GAAkCE,GAAkC,kBAApBA,EAAW/O,MAA4B,kDAAkDpB,KAAKmQ,EAAWlL,QAAQlE,kBACnKsH,GAAc,IAQpB,MAJAjM,GAAYM,UAAY2L,EACpB3F,IAAkB2F,GAAgB2H,GACpCM,GAAoBzZ,GAEfwR,GAWLiI,GAAsB,SAASxZ,GAQjC,QAASyZ,GAAkBC,GACzB,GAAIvV,GAAUuV,EAAKtV,MAAM,SAEzB,OADAD,GAAQpB,OAAS,EACVoB,EAAQyD,KAAK,KAEtB,QAAS+R,GAAcC,GACrB,QAASA,IAAwBA,EAAsBA,EAAoB3P,iBAAmB,0EAA0Ef,KAAK0Q,IAA2D,kBAAnCA,EAAoBrY,MAAM,MAEjO,QAASsY,GAAcC,GACjBA,IACFC,GAAW,EACPD,EAAOtU,UACTwU,EAAeP,EAAkBK,EAAOtU,WAErCwU,GAAgBF,EAAOG,cAC1BD,EAAeP,EAAkBK,EAAOG,cAEtCH,EAAOI,WACTC,EAAUR,EAAcG,EAAOI,YAzBrC,GAAIJ,GAAQM,EAAIC,EAAUN,GAAW,EAAOO,GAAY,EAAOH,GAAU,EAAOH,EAAe,EA6B/F,IAAI/a,EAAWsb,SAAWtb,EAAWsb,QAAQxX,OAC3C+W,EAAS7a,EAAWsb,QAAQ,mBAC5BV,EAAcC,GACV7a,EAAWsb,QAAQ,yBACrBR,GAAW,EACXC,EAAe,gBAEZ,IAAI/a,EAAWub,WAAavb,EAAWub,UAAUzX,OACtDsX,EAAWpb,EAAWub,UAAU,iCAChCV,EAASO,GAAYA,EAASI,cAC9BZ,EAAcC,OACT,IAA6B,mBAAlB9Z,GAA+B,CAC/Csa,GAAY,CACZ,KACEF,EAAK,GAAIpa,GAAc,mCACvB+Z,GAAW,EACXC,EAAeP,EAAkBW,EAAGM,YAAY,aAChD,MAAOC,GACP,IACEP,EAAK,GAAIpa,GAAc,mCACvB+Z,GAAW,EACXC,EAAe,SACf,MAAOY,GACP,IACER,EAAK,GAAIpa,GAAc,iCACvB+Z,GAAW,EACXC,EAAeP,EAAkBW,EAAGM,YAAY,aAChD,MAAOG,GACPP,GAAY,KAKpBhV,EAAYI,SAAWqU,KAAa,EACpCzU,EAAYK,SAAWqU,GAAgB1Z,EAAY0Z,GAAgB1Z,EAAY4F,GAC/EZ,EAAYE,QAAUwU,GAAgB,QACtC1U,EAAYG,WAAa0U,EAAU,SAAWG,EAAY,UAAYP,EAAW,WAAa,UAKhGP,IAAoBzZ,GAIpBsJ,IAAe,EAMf,IAAII,IAAgB,WAClB,MAAM8B,gBAAgB9B,SAGqB,kBAAhCA,IAAcqR,eACvBrR,GAAcqR,cAAc/J,MAAMxF,KAAMrJ,EAAMW,aAHvC,GAAI4G,IAafzI,GAAgByI,GAAe,WAC7BqI,MAAO,QACPiJ,UAAU,EACVC,cAAc,EACdC,YAAY,IASdxR,GAAcC,OAAS,WACrB,MAAOV,GAAQ+H,MAAMxF,KAAMrJ,EAAMW,aAQnC4G,GAAcyR,MAAQ,WACpB,MAAO9R,GAAO2H,MAAMxF,KAAMrJ,EAAMW,aAQlC4G,GAAcoC,gBAAkB,WAC9B,MAAOlC,IAAiBoH,MAAMxF,KAAMrJ,EAAMW,aAQ5C4G,GAAcS,GAAK,WACjB,MAAON,IAAImH,MAAMxF,KAAMrJ,EAAMW,aAU/B4G,GAAcmB,IAAM,WAClB,MAAOH,IAAKsG,MAAMxF,KAAMrJ,EAAMW,aAQhC4G,GAAcyH,SAAW,WACvB,MAAOpG,IAAWiG,MAAMxF,KAAMrJ,EAAMW,aAQtC4G,GAAcW,KAAO,WACnB,MAAOW,IAAMgG,MAAMxF,KAAMrJ,EAAMW,aAQjC4G,GAAc0R,OAAS,WACrB,MAAOxP,IAAQoF,MAAMxF,KAAMrJ,EAAMW,aAQnC4G,GAAc2R,QAAU,WACtB,MAAOpP,IAAS+E,MAAMxF,KAAMrJ,EAAMW,aAQpC4G,GAAc6E,QAAU,WACtB,MAAOlC,IAAS2E,MAAMxF,KAAMrJ,EAAMW,aASpC4G,GAAcwC,UAAY,WACxB,MAAOO,IAAWuE,MAAMxF,KAAMrJ,EAAMW,aAStC4G,GAAc4R,QAAU,WACtB,MAAO5O,IAASsE,MAAMxF,KAAMrJ,EAAMW,aAWpC4G,GAAc2I,MAAQ3I,GAAc6R,SAAW,WAC7C,MAAO5O,IAAOqE,MAAMxF,KAAMrJ,EAAMW,aAUlC4G,GAAcyC,KAAOzC,GAAc8R,WAAa,WAC9C,MAAOhO,IAAMwD,MAAMxF,KAAMrJ,EAAMW,aAQjC4G,GAAcuN,cAAgB,WAC5B,MAAOnJ,IAAekD,MAAMxF,KAAMrJ,EAAMW,aAEpB,kBAAX2Y,SAAyBA,OAAOC,IACzCD,OAAO,WACL,MAAO/R,MAEkB,gBAAXiS,SAAuBA,QAAoC,gBAAnBA,QAAOC,SAAwBD,OAAOC,QAC9FD,OAAOC,QAAUlS,GAEjBhL,EAAOgL,cAAgBA,IAExB,WACD,MAAO8B,OAAQ9M", "sourcesContent": ["/*!\n * ZeroClipboard\n * The ZeroClipboard library provides an easy way to copy text to the clipboard using an invisible Adobe Flash movie and a JavaScript interface.\n * Copyright (c) 2009-2014 <PERSON>, <PERSON>\n * Licensed MIT\n * http://zeroclipboard.org/\n * v2.2.0\n */\n(function(window, undefined) {\n  \"use strict\";\n  /**\n * Store references to critically important global functions that may be\n * overridden on certain web pages.\n */\n  var _window = window, _document = _window.document, _navigator = _window.navigator, _setTimeout = _window.setTimeout, _clearTimeout = _window.clearTimeout, _setInterval = _window.setInterval, _clearInterval = _window.clearInterval, _getComputedStyle = _window.getComputedStyle, _encodeURIComponent = _window.encodeURIComponent, _ActiveXObject = _window.ActiveXObject, _Error = _window.Error, _parseInt = _window.Number.parseInt || _window.parseInt, _parseFloat = _window.Number.parseFloat || _window.parseFloat, _isNaN = _window.Number.isNaN || _window.isNaN, _now = _window.Date.now, _keys = _window.Object.keys, _defineProperty = _window.Object.defineProperty, _hasOwn = _window.Object.prototype.hasOwnProperty, _slice = _window.Array.prototype.slice, _unwrap = function() {\n    var unwrapper = function(el) {\n      return el;\n    };\n    if (typeof _window.wrap === \"function\" && typeof _window.unwrap === \"function\") {\n      try {\n        var div = _document.createElement(\"div\");\n        var unwrappedDiv = _window.unwrap(div);\n        if (div.nodeType === 1 && unwrappedDiv && unwrappedDiv.nodeType === 1) {\n          unwrapper = _window.unwrap;\n        }\n      } catch (e) {}\n    }\n    return unwrapper;\n  }();\n  /**\n * Convert an `arguments` object into an Array.\n *\n * @returns The arguments as an Array\n * @private\n */\n  var _args = function(argumentsObj) {\n    return _slice.call(argumentsObj, 0);\n  };\n  /**\n * Shallow-copy the owned, enumerable properties of one object over to another, similar to jQuery's `$.extend`.\n *\n * @returns The target object, augmented\n * @private\n */\n  var _extend = function() {\n    var i, len, arg, prop, src, copy, args = _args(arguments), target = args[0] || {};\n    for (i = 1, len = args.length; i < len; i++) {\n      if ((arg = args[i]) != null) {\n        for (prop in arg) {\n          if (_hasOwn.call(arg, prop)) {\n            src = target[prop];\n            copy = arg[prop];\n            if (target !== copy && copy !== undefined) {\n              target[prop] = copy;\n            }\n          }\n        }\n      }\n    }\n    return target;\n  };\n  /**\n * Return a deep copy of the source object or array.\n *\n * @returns Object or Array\n * @private\n */\n  var _deepCopy = function(source) {\n    var copy, i, len, prop;\n    if (typeof source !== \"object\" || source == null || typeof source.nodeType === \"number\") {\n      copy = source;\n    } else if (typeof source.length === \"number\") {\n      copy = [];\n      for (i = 0, len = source.length; i < len; i++) {\n        if (_hasOwn.call(source, i)) {\n          copy[i] = _deepCopy(source[i]);\n        }\n      }\n    } else {\n      copy = {};\n      for (prop in source) {\n        if (_hasOwn.call(source, prop)) {\n          copy[prop] = _deepCopy(source[prop]);\n        }\n      }\n    }\n    return copy;\n  };\n  /**\n * Makes a shallow copy of `obj` (like `_extend`) but filters its properties based on a list of `keys` to keep.\n * The inverse of `_omit`, mostly. The big difference is that these properties do NOT need to be enumerable to\n * be kept.\n *\n * @returns A new filtered object.\n * @private\n */\n  var _pick = function(obj, keys) {\n    var newObj = {};\n    for (var i = 0, len = keys.length; i < len; i++) {\n      if (keys[i] in obj) {\n        newObj[keys[i]] = obj[keys[i]];\n      }\n    }\n    return newObj;\n  };\n  /**\n * Makes a shallow copy of `obj` (like `_extend`) but filters its properties based on a list of `keys` to omit.\n * The inverse of `_pick`.\n *\n * @returns A new filtered object.\n * @private\n */\n  var _omit = function(obj, keys) {\n    var newObj = {};\n    for (var prop in obj) {\n      if (keys.indexOf(prop) === -1) {\n        newObj[prop] = obj[prop];\n      }\n    }\n    return newObj;\n  };\n  /**\n * Remove all owned, enumerable properties from an object.\n *\n * @returns The original object without its owned, enumerable properties.\n * @private\n */\n  var _deleteOwnProperties = function(obj) {\n    if (obj) {\n      for (var prop in obj) {\n        if (_hasOwn.call(obj, prop)) {\n          delete obj[prop];\n        }\n      }\n    }\n    return obj;\n  };\n  /**\n * Determine if an element is contained within another element.\n *\n * @returns Boolean\n * @private\n */\n  var _containedBy = function(el, ancestorEl) {\n    if (el && el.nodeType === 1 && el.ownerDocument && ancestorEl && (ancestorEl.nodeType === 1 && ancestorEl.ownerDocument && ancestorEl.ownerDocument === el.ownerDocument || ancestorEl.nodeType === 9 && !ancestorEl.ownerDocument && ancestorEl === el.ownerDocument)) {\n      do {\n        if (el === ancestorEl) {\n          return true;\n        }\n        el = el.parentNode;\n      } while (el);\n    }\n    return false;\n  };\n  /**\n * Get the URL path's parent directory.\n *\n * @returns String or `undefined`\n * @private\n */\n  var _getDirPathOfUrl = function(url) {\n    var dir;\n    if (typeof url === \"string\" && url) {\n      dir = url.split(\"#\")[0].split(\"?\")[0];\n      dir = url.slice(0, url.lastIndexOf(\"/\") + 1);\n    }\n    return dir;\n  };\n  /**\n * Get the current script's URL by throwing an `Error` and analyzing it.\n *\n * @returns String or `undefined`\n * @private\n */\n  var _getCurrentScriptUrlFromErrorStack = function(stack) {\n    var url, matches;\n    if (typeof stack === \"string\" && stack) {\n      matches = stack.match(/^(?:|[^:@]*@|.+\\)@(?=http[s]?|file)|.+?\\s+(?: at |@)(?:[^:\\(]+ )*[\\(]?)((?:http[s]?|file):\\/\\/[\\/]?.+?\\/[^:\\)]*?)(?::\\d+)(?::\\d+)?/);\n      if (matches && matches[1]) {\n        url = matches[1];\n      } else {\n        matches = stack.match(/\\)@((?:http[s]?|file):\\/\\/[\\/]?.+?\\/[^:\\)]*?)(?::\\d+)(?::\\d+)?/);\n        if (matches && matches[1]) {\n          url = matches[1];\n        }\n      }\n    }\n    return url;\n  };\n  /**\n * Get the current script's URL by throwing an `Error` and analyzing it.\n *\n * @returns String or `undefined`\n * @private\n */\n  var _getCurrentScriptUrlFromError = function() {\n    var url, err;\n    try {\n      throw new _Error();\n    } catch (e) {\n      err = e;\n    }\n    if (err) {\n      url = err.sourceURL || err.fileName || _getCurrentScriptUrlFromErrorStack(err.stack);\n    }\n    return url;\n  };\n  /**\n * Get the current script's URL.\n *\n * @returns String or `undefined`\n * @private\n */\n  var _getCurrentScriptUrl = function() {\n    var jsPath, scripts, i;\n    if (_document.currentScript && (jsPath = _document.currentScript.src)) {\n      return jsPath;\n    }\n    scripts = _document.getElementsByTagName(\"script\");\n    if (scripts.length === 1) {\n      return scripts[0].src || undefined;\n    }\n    if (\"readyState\" in scripts[0]) {\n      for (i = scripts.length; i--; ) {\n        if (scripts[i].readyState === \"interactive\" && (jsPath = scripts[i].src)) {\n          return jsPath;\n        }\n      }\n    }\n    if (_document.readyState === \"loading\" && (jsPath = scripts[scripts.length - 1].src)) {\n      return jsPath;\n    }\n    if (jsPath = _getCurrentScriptUrlFromError()) {\n      return jsPath;\n    }\n    return undefined;\n  };\n  /**\n * Get the unanimous parent directory of ALL script tags.\n * If any script tags are either (a) inline or (b) from differing parent\n * directories, this method must return `undefined`.\n *\n * @returns String or `undefined`\n * @private\n */\n  var _getUnanimousScriptParentDir = function() {\n    var i, jsDir, jsPath, scripts = _document.getElementsByTagName(\"script\");\n    for (i = scripts.length; i--; ) {\n      if (!(jsPath = scripts[i].src)) {\n        jsDir = null;\n        break;\n      }\n      jsPath = _getDirPathOfUrl(jsPath);\n      if (jsDir == null) {\n        jsDir = jsPath;\n      } else if (jsDir !== jsPath) {\n        jsDir = null;\n        break;\n      }\n    }\n    return jsDir || undefined;\n  };\n  /**\n * Get the presumed location of the \"ZeroClipboard.swf\" file, based on the location\n * of the executing JavaScript file (e.g. \"ZeroClipboard.js\", etc.).\n *\n * @returns String\n * @private\n */\n  var _getDefaultSwfPath = function() {\n    var jsDir = _getDirPathOfUrl(_getCurrentScriptUrl()) || _getUnanimousScriptParentDir() || \"\";\n    return jsDir + \"ZeroClipboard.swf\";\n  };\n  /**\n * Keep track of if the page is framed (in an `iframe`). This can never change.\n * @private\n */\n  var _pageIsFramed = function() {\n    return window.opener == null && (!!window.top && window != window.top || !!window.parent && window != window.parent);\n  }();\n  /**\n * Keep track of the state of the Flash object.\n * @private\n */\n  var _flashState = {\n    bridge: null,\n    version: \"0.0.0\",\n    pluginType: \"unknown\",\n    disabled: null,\n    outdated: null,\n    sandboxed: null,\n    unavailable: null,\n    degraded: null,\n    deactivated: null,\n    overdue: null,\n    ready: null\n  };\n  /**\n * The minimum Flash Player version required to use ZeroClipboard completely.\n * @readonly\n * @private\n */\n  var _minimumFlashVersion = \"11.0.0\";\n  /**\n * The ZeroClipboard library version number, as reported by Flash, at the time the SWF was compiled.\n */\n  var _zcSwfVersion;\n  /**\n * Keep track of all event listener registrations.\n * @private\n */\n  var _handlers = {};\n  /**\n * Keep track of the currently activated element.\n * @private\n */\n  var _currentElement;\n  /**\n * Keep track of the element that was activated when a `copy` process started.\n * @private\n */\n  var _copyTarget;\n  /**\n * Keep track of data for the pending clipboard transaction.\n * @private\n */\n  var _clipData = {};\n  /**\n * Keep track of data formats for the pending clipboard transaction.\n * @private\n */\n  var _clipDataFormatMap = null;\n  /**\n * Keep track of the Flash availability check timeout.\n * @private\n */\n  var _flashCheckTimeout = 0;\n  /**\n * Keep track of SWF network errors interval polling.\n * @private\n */\n  var _swfFallbackCheckInterval = 0;\n  /**\n * The `message` store for events\n * @private\n */\n  var _eventMessages = {\n    ready: \"Flash communication is established\",\n    error: {\n      \"flash-disabled\": \"Flash is disabled or not installed. May also be attempting to run Flash in a sandboxed iframe, which is impossible.\",\n      \"flash-outdated\": \"Flash is too outdated to support ZeroClipboard\",\n      \"flash-sandboxed\": \"Attempting to run Flash in a sandboxed iframe, which is impossible\",\n      \"flash-unavailable\": \"Flash is unable to communicate bidirectionally with JavaScript\",\n      \"flash-degraded\": \"Flash is unable to preserve data fidelity when communicating with JavaScript\",\n      \"flash-deactivated\": \"Flash is too outdated for your browser and/or is configured as click-to-activate.\\nThis may also mean that the ZeroClipboard SWF object could not be loaded, so please check your `swfPath` configuration and/or network connectivity.\\nMay also be attempting to run Flash in a sandboxed iframe, which is impossible.\",\n      \"flash-overdue\": \"Flash communication was established but NOT within the acceptable time limit\",\n      \"version-mismatch\": \"ZeroClipboard JS version number does not match ZeroClipboard SWF version number\",\n      \"clipboard-error\": \"At least one error was thrown while ZeroClipboard was attempting to inject your data into the clipboard\",\n      \"config-mismatch\": \"ZeroClipboard configuration does not match Flash's reality\",\n      \"swf-not-found\": \"The ZeroClipboard SWF object could not be loaded, so please check your `swfPath` configuration and/or network connectivity\"\n    }\n  };\n  /**\n * The `name`s of `error` events that can only occur is Flash has at least\n * been able to load the SWF successfully.\n * @private\n */\n  var _errorsThatOnlyOccurAfterFlashLoads = [ \"flash-unavailable\", \"flash-degraded\", \"flash-overdue\", \"version-mismatch\", \"config-mismatch\", \"clipboard-error\" ];\n  /**\n * The `name`s of `error` events that should likely result in the `_flashState`\n * variable's property values being updated.\n * @private\n */\n  var _flashStateErrorNames = [ \"flash-disabled\", \"flash-outdated\", \"flash-sandboxed\", \"flash-unavailable\", \"flash-degraded\", \"flash-deactivated\", \"flash-overdue\" ];\n  /**\n * A RegExp to match the `name` property of `error` events related to Flash.\n * @private\n */\n  var _flashStateErrorNameMatchingRegex = new RegExp(\"^flash-(\" + _flashStateErrorNames.map(function(errorName) {\n    return errorName.replace(/^flash-/, \"\");\n  }).join(\"|\") + \")$\");\n  /**\n * A RegExp to match the `name` property of `error` events related to Flash,\n * which is enabled.\n * @private\n */\n  var _flashStateEnabledErrorNameMatchingRegex = new RegExp(\"^flash-(\" + _flashStateErrorNames.slice(1).map(function(errorName) {\n    return errorName.replace(/^flash-/, \"\");\n  }).join(\"|\") + \")$\");\n  /**\n * ZeroClipboard configuration defaults for the Core module.\n * @private\n */\n  var _globalConfig = {\n    swfPath: _getDefaultSwfPath(),\n    trustedDomains: window.location.host ? [ window.location.host ] : [],\n    cacheBust: true,\n    forceEnhancedClipboard: false,\n    flashLoadTimeout: 3e4,\n    autoActivate: true,\n    bubbleEvents: true,\n    containerId: \"global-zeroclipboard-html-bridge\",\n    containerClass: \"global-zeroclipboard-container\",\n    swfObjectId: \"global-zeroclipboard-flash-bridge\",\n    hoverClass: \"zeroclipboard-is-hover\",\n    activeClass: \"zeroclipboard-is-active\",\n    forceHandCursor: false,\n    title: null,\n    zIndex: 999999999\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.config`.\n * @private\n */\n  var _config = function(options) {\n    if (typeof options === \"object\" && options !== null) {\n      for (var prop in options) {\n        if (_hasOwn.call(options, prop)) {\n          if (/^(?:forceHandCursor|title|zIndex|bubbleEvents)$/.test(prop)) {\n            _globalConfig[prop] = options[prop];\n          } else if (_flashState.bridge == null) {\n            if (prop === \"containerId\" || prop === \"swfObjectId\") {\n              if (_isValidHtml4Id(options[prop])) {\n                _globalConfig[prop] = options[prop];\n              } else {\n                throw new Error(\"The specified `\" + prop + \"` value is not valid as an HTML4 Element ID\");\n              }\n            } else {\n              _globalConfig[prop] = options[prop];\n            }\n          }\n        }\n      }\n    }\n    if (typeof options === \"string\" && options) {\n      if (_hasOwn.call(_globalConfig, options)) {\n        return _globalConfig[options];\n      }\n      return;\n    }\n    return _deepCopy(_globalConfig);\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.state`.\n * @private\n */\n  var _state = function() {\n    _detectSandbox();\n    return {\n      browser: _pick(_navigator, [ \"userAgent\", \"platform\", \"appName\" ]),\n      flash: _omit(_flashState, [ \"bridge\" ]),\n      zeroclipboard: {\n        version: ZeroClipboard.version,\n        config: ZeroClipboard.config()\n      }\n    };\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.isFlashUnusable`.\n * @private\n */\n  var _isFlashUnusable = function() {\n    return !!(_flashState.disabled || _flashState.outdated || _flashState.sandboxed || _flashState.unavailable || _flashState.degraded || _flashState.deactivated);\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.on`.\n * @private\n */\n  var _on = function(eventType, listener) {\n    var i, len, events, added = {};\n    if (typeof eventType === \"string\" && eventType) {\n      events = eventType.toLowerCase().split(/\\s+/);\n    } else if (typeof eventType === \"object\" && eventType && typeof listener === \"undefined\") {\n      for (i in eventType) {\n        if (_hasOwn.call(eventType, i) && typeof i === \"string\" && i && typeof eventType[i] === \"function\") {\n          ZeroClipboard.on(i, eventType[i]);\n        }\n      }\n    }\n    if (events && events.length) {\n      for (i = 0, len = events.length; i < len; i++) {\n        eventType = events[i].replace(/^on/, \"\");\n        added[eventType] = true;\n        if (!_handlers[eventType]) {\n          _handlers[eventType] = [];\n        }\n        _handlers[eventType].push(listener);\n      }\n      if (added.ready && _flashState.ready) {\n        ZeroClipboard.emit({\n          type: \"ready\"\n        });\n      }\n      if (added.error) {\n        for (i = 0, len = _flashStateErrorNames.length; i < len; i++) {\n          if (_flashState[_flashStateErrorNames[i].replace(/^flash-/, \"\")] === true) {\n            ZeroClipboard.emit({\n              type: \"error\",\n              name: _flashStateErrorNames[i]\n            });\n            break;\n          }\n        }\n        if (_zcSwfVersion !== undefined && ZeroClipboard.version !== _zcSwfVersion) {\n          ZeroClipboard.emit({\n            type: \"error\",\n            name: \"version-mismatch\",\n            jsVersion: ZeroClipboard.version,\n            swfVersion: _zcSwfVersion\n          });\n        }\n      }\n    }\n    return ZeroClipboard;\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.off`.\n * @private\n */\n  var _off = function(eventType, listener) {\n    var i, len, foundIndex, events, perEventHandlers;\n    if (arguments.length === 0) {\n      events = _keys(_handlers);\n    } else if (typeof eventType === \"string\" && eventType) {\n      events = eventType.split(/\\s+/);\n    } else if (typeof eventType === \"object\" && eventType && typeof listener === \"undefined\") {\n      for (i in eventType) {\n        if (_hasOwn.call(eventType, i) && typeof i === \"string\" && i && typeof eventType[i] === \"function\") {\n          ZeroClipboard.off(i, eventType[i]);\n        }\n      }\n    }\n    if (events && events.length) {\n      for (i = 0, len = events.length; i < len; i++) {\n        eventType = events[i].toLowerCase().replace(/^on/, \"\");\n        perEventHandlers = _handlers[eventType];\n        if (perEventHandlers && perEventHandlers.length) {\n          if (listener) {\n            foundIndex = perEventHandlers.indexOf(listener);\n            while (foundIndex !== -1) {\n              perEventHandlers.splice(foundIndex, 1);\n              foundIndex = perEventHandlers.indexOf(listener, foundIndex);\n            }\n          } else {\n            perEventHandlers.length = 0;\n          }\n        }\n      }\n    }\n    return ZeroClipboard;\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.handlers`.\n * @private\n */\n  var _listeners = function(eventType) {\n    var copy;\n    if (typeof eventType === \"string\" && eventType) {\n      copy = _deepCopy(_handlers[eventType]) || null;\n    } else {\n      copy = _deepCopy(_handlers);\n    }\n    return copy;\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.emit`.\n * @private\n */\n  var _emit = function(event) {\n    var eventCopy, returnVal, tmp;\n    event = _createEvent(event);\n    if (!event) {\n      return;\n    }\n    if (_preprocessEvent(event)) {\n      return;\n    }\n    if (event.type === \"ready\" && _flashState.overdue === true) {\n      return ZeroClipboard.emit({\n        type: \"error\",\n        name: \"flash-overdue\"\n      });\n    }\n    eventCopy = _extend({}, event);\n    _dispatchCallbacks.call(this, eventCopy);\n    if (event.type === \"copy\") {\n      tmp = _mapClipDataToFlash(_clipData);\n      returnVal = tmp.data;\n      _clipDataFormatMap = tmp.formatMap;\n    }\n    return returnVal;\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.create`.\n * @private\n */\n  var _create = function() {\n    var previousState = _flashState.sandboxed;\n    _detectSandbox();\n    if (typeof _flashState.ready !== \"boolean\") {\n      _flashState.ready = false;\n    }\n    if (_flashState.sandboxed !== previousState && _flashState.sandboxed === true) {\n      _flashState.ready = false;\n      ZeroClipboard.emit({\n        type: \"error\",\n        name: \"flash-sandboxed\"\n      });\n    } else if (!ZeroClipboard.isFlashUnusable() && _flashState.bridge === null) {\n      var maxWait = _globalConfig.flashLoadTimeout;\n      if (typeof maxWait === \"number\" && maxWait >= 0) {\n        _flashCheckTimeout = _setTimeout(function() {\n          if (typeof _flashState.deactivated !== \"boolean\") {\n            _flashState.deactivated = true;\n          }\n          if (_flashState.deactivated === true) {\n            ZeroClipboard.emit({\n              type: \"error\",\n              name: \"flash-deactivated\"\n            });\n          }\n        }, maxWait);\n      }\n      _flashState.overdue = false;\n      _embedSwf();\n    }\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.destroy`.\n * @private\n */\n  var _destroy = function() {\n    ZeroClipboard.clearData();\n    ZeroClipboard.blur();\n    ZeroClipboard.emit(\"destroy\");\n    _unembedSwf();\n    ZeroClipboard.off();\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.setData`.\n * @private\n */\n  var _setData = function(format, data) {\n    var dataObj;\n    if (typeof format === \"object\" && format && typeof data === \"undefined\") {\n      dataObj = format;\n      ZeroClipboard.clearData();\n    } else if (typeof format === \"string\" && format) {\n      dataObj = {};\n      dataObj[format] = data;\n    } else {\n      return;\n    }\n    for (var dataFormat in dataObj) {\n      if (typeof dataFormat === \"string\" && dataFormat && _hasOwn.call(dataObj, dataFormat) && typeof dataObj[dataFormat] === \"string\" && dataObj[dataFormat]) {\n        _clipData[dataFormat] = dataObj[dataFormat];\n      }\n    }\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.clearData`.\n * @private\n */\n  var _clearData = function(format) {\n    if (typeof format === \"undefined\") {\n      _deleteOwnProperties(_clipData);\n      _clipDataFormatMap = null;\n    } else if (typeof format === \"string\" && _hasOwn.call(_clipData, format)) {\n      delete _clipData[format];\n    }\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.getData`.\n * @private\n */\n  var _getData = function(format) {\n    if (typeof format === \"undefined\") {\n      return _deepCopy(_clipData);\n    } else if (typeof format === \"string\" && _hasOwn.call(_clipData, format)) {\n      return _clipData[format];\n    }\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.focus`/`ZeroClipboard.activate`.\n * @private\n */\n  var _focus = function(element) {\n    if (!(element && element.nodeType === 1)) {\n      return;\n    }\n    if (_currentElement) {\n      _removeClass(_currentElement, _globalConfig.activeClass);\n      if (_currentElement !== element) {\n        _removeClass(_currentElement, _globalConfig.hoverClass);\n      }\n    }\n    _currentElement = element;\n    _addClass(element, _globalConfig.hoverClass);\n    var newTitle = element.getAttribute(\"title\") || _globalConfig.title;\n    if (typeof newTitle === \"string\" && newTitle) {\n      var htmlBridge = _getHtmlBridge(_flashState.bridge);\n      if (htmlBridge) {\n        htmlBridge.setAttribute(\"title\", newTitle);\n      }\n    }\n    var useHandCursor = _globalConfig.forceHandCursor === true || _getStyle(element, \"cursor\") === \"pointer\";\n    _setHandCursor(useHandCursor);\n    _reposition();\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.blur`/`ZeroClipboard.deactivate`.\n * @private\n */\n  var _blur = function() {\n    var htmlBridge = _getHtmlBridge(_flashState.bridge);\n    if (htmlBridge) {\n      htmlBridge.removeAttribute(\"title\");\n      htmlBridge.style.left = \"0px\";\n      htmlBridge.style.top = \"-9999px\";\n      htmlBridge.style.width = \"1px\";\n      htmlBridge.style.height = \"1px\";\n    }\n    if (_currentElement) {\n      _removeClass(_currentElement, _globalConfig.hoverClass);\n      _removeClass(_currentElement, _globalConfig.activeClass);\n      _currentElement = null;\n    }\n  };\n  /**\n * The underlying implementation of `ZeroClipboard.activeElement`.\n * @private\n */\n  var _activeElement = function() {\n    return _currentElement || null;\n  };\n  /**\n * Check if a value is a valid HTML4 `ID` or `Name` token.\n * @private\n */\n  var _isValidHtml4Id = function(id) {\n    return typeof id === \"string\" && id && /^[A-Za-z][A-Za-z0-9_:\\-\\.]*$/.test(id);\n  };\n  /**\n * Create or update an `event` object, based on the `eventType`.\n * @private\n */\n  var _createEvent = function(event) {\n    var eventType;\n    if (typeof event === \"string\" && event) {\n      eventType = event;\n      event = {};\n    } else if (typeof event === \"object\" && event && typeof event.type === \"string\" && event.type) {\n      eventType = event.type;\n    }\n    if (!eventType) {\n      return;\n    }\n    eventType = eventType.toLowerCase();\n    if (!event.target && (/^(copy|aftercopy|_click)$/.test(eventType) || eventType === \"error\" && event.name === \"clipboard-error\")) {\n      event.target = _copyTarget;\n    }\n    _extend(event, {\n      type: eventType,\n      target: event.target || _currentElement || null,\n      relatedTarget: event.relatedTarget || null,\n      currentTarget: _flashState && _flashState.bridge || null,\n      timeStamp: event.timeStamp || _now() || null\n    });\n    var msg = _eventMessages[event.type];\n    if (event.type === \"error\" && event.name && msg) {\n      msg = msg[event.name];\n    }\n    if (msg) {\n      event.message = msg;\n    }\n    if (event.type === \"ready\") {\n      _extend(event, {\n        target: null,\n        version: _flashState.version\n      });\n    }\n    if (event.type === \"error\") {\n      if (_flashStateErrorNameMatchingRegex.test(event.name)) {\n        _extend(event, {\n          target: null,\n          minimumVersion: _minimumFlashVersion\n        });\n      }\n      if (_flashStateEnabledErrorNameMatchingRegex.test(event.name)) {\n        _extend(event, {\n          version: _flashState.version\n        });\n      }\n    }\n    if (event.type === \"copy\") {\n      event.clipboardData = {\n        setData: ZeroClipboard.setData,\n        clearData: ZeroClipboard.clearData\n      };\n    }\n    if (event.type === \"aftercopy\") {\n      event = _mapClipResultsFromFlash(event, _clipDataFormatMap);\n    }\n    if (event.target && !event.relatedTarget) {\n      event.relatedTarget = _getRelatedTarget(event.target);\n    }\n    return _addMouseData(event);\n  };\n  /**\n * Get a relatedTarget from the target's `data-clipboard-target` attribute\n * @private\n */\n  var _getRelatedTarget = function(targetEl) {\n    var relatedTargetId = targetEl && targetEl.getAttribute && targetEl.getAttribute(\"data-clipboard-target\");\n    return relatedTargetId ? _document.getElementById(relatedTargetId) : null;\n  };\n  /**\n * Add element and position data to `MouseEvent` instances\n * @private\n */\n  var _addMouseData = function(event) {\n    if (event && /^_(?:click|mouse(?:over|out|down|up|move))$/.test(event.type)) {\n      var srcElement = event.target;\n      var fromElement = event.type === \"_mouseover\" && event.relatedTarget ? event.relatedTarget : undefined;\n      var toElement = event.type === \"_mouseout\" && event.relatedTarget ? event.relatedTarget : undefined;\n      var pos = _getElementPosition(srcElement);\n      var screenLeft = _window.screenLeft || _window.screenX || 0;\n      var screenTop = _window.screenTop || _window.screenY || 0;\n      var scrollLeft = _document.body.scrollLeft + _document.documentElement.scrollLeft;\n      var scrollTop = _document.body.scrollTop + _document.documentElement.scrollTop;\n      var pageX = pos.left + (typeof event._stageX === \"number\" ? event._stageX : 0);\n      var pageY = pos.top + (typeof event._stageY === \"number\" ? event._stageY : 0);\n      var clientX = pageX - scrollLeft;\n      var clientY = pageY - scrollTop;\n      var screenX = screenLeft + clientX;\n      var screenY = screenTop + clientY;\n      var moveX = typeof event.movementX === \"number\" ? event.movementX : 0;\n      var moveY = typeof event.movementY === \"number\" ? event.movementY : 0;\n      delete event._stageX;\n      delete event._stageY;\n      _extend(event, {\n        srcElement: srcElement,\n        fromElement: fromElement,\n        toElement: toElement,\n        screenX: screenX,\n        screenY: screenY,\n        pageX: pageX,\n        pageY: pageY,\n        clientX: clientX,\n        clientY: clientY,\n        x: clientX,\n        y: clientY,\n        movementX: moveX,\n        movementY: moveY,\n        offsetX: 0,\n        offsetY: 0,\n        layerX: 0,\n        layerY: 0\n      });\n    }\n    return event;\n  };\n  /**\n * Determine if an event's registered handlers should be execute synchronously or asynchronously.\n *\n * @returns {boolean}\n * @private\n */\n  var _shouldPerformAsync = function(event) {\n    var eventType = event && typeof event.type === \"string\" && event.type || \"\";\n    return !/^(?:(?:before)?copy|destroy)$/.test(eventType);\n  };\n  /**\n * Control if a callback should be executed asynchronously or not.\n *\n * @returns `undefined`\n * @private\n */\n  var _dispatchCallback = function(func, context, args, async) {\n    if (async) {\n      _setTimeout(function() {\n        func.apply(context, args);\n      }, 0);\n    } else {\n      func.apply(context, args);\n    }\n  };\n  /**\n * Handle the actual dispatching of events to client instances.\n *\n * @returns `undefined`\n * @private\n */\n  var _dispatchCallbacks = function(event) {\n    if (!(typeof event === \"object\" && event && event.type)) {\n      return;\n    }\n    var async = _shouldPerformAsync(event);\n    var wildcardTypeHandlers = _handlers[\"*\"] || [];\n    var specificTypeHandlers = _handlers[event.type] || [];\n    var handlers = wildcardTypeHandlers.concat(specificTypeHandlers);\n    if (handlers && handlers.length) {\n      var i, len, func, context, eventCopy, originalContext = this;\n      for (i = 0, len = handlers.length; i < len; i++) {\n        func = handlers[i];\n        context = originalContext;\n        if (typeof func === \"string\" && typeof _window[func] === \"function\") {\n          func = _window[func];\n        }\n        if (typeof func === \"object\" && func && typeof func.handleEvent === \"function\") {\n          context = func;\n          func = func.handleEvent;\n        }\n        if (typeof func === \"function\") {\n          eventCopy = _extend({}, event);\n          _dispatchCallback(func, context, [ eventCopy ], async);\n        }\n      }\n    }\n    return this;\n  };\n  /**\n * Check an `error` event's `name` property to see if Flash has\n * already loaded, which rules out possible `iframe` sandboxing.\n * @private\n */\n  var _getSandboxStatusFromErrorEvent = function(event) {\n    var isSandboxed = null;\n    if (_pageIsFramed === false || event && event.type === \"error\" && event.name && _errorsThatOnlyOccurAfterFlashLoads.indexOf(event.name) !== -1) {\n      isSandboxed = false;\n    }\n    return isSandboxed;\n  };\n  /**\n * Preprocess any special behaviors, reactions, or state changes after receiving this event.\n * Executes only once per event emitted, NOT once per client.\n * @private\n */\n  var _preprocessEvent = function(event) {\n    var element = event.target || _currentElement || null;\n    var sourceIsSwf = event._source === \"swf\";\n    delete event._source;\n    switch (event.type) {\n     case \"error\":\n      var isSandboxed = event.name === \"flash-sandboxed\" || _getSandboxStatusFromErrorEvent(event);\n      if (typeof isSandboxed === \"boolean\") {\n        _flashState.sandboxed = isSandboxed;\n      }\n      if (_flashStateErrorNames.indexOf(event.name) !== -1) {\n        _extend(_flashState, {\n          disabled: event.name === \"flash-disabled\",\n          outdated: event.name === \"flash-outdated\",\n          unavailable: event.name === \"flash-unavailable\",\n          degraded: event.name === \"flash-degraded\",\n          deactivated: event.name === \"flash-deactivated\",\n          overdue: event.name === \"flash-overdue\",\n          ready: false\n        });\n      } else if (event.name === \"version-mismatch\") {\n        _zcSwfVersion = event.swfVersion;\n        _extend(_flashState, {\n          disabled: false,\n          outdated: false,\n          unavailable: false,\n          degraded: false,\n          deactivated: false,\n          overdue: false,\n          ready: false\n        });\n      }\n      _clearTimeoutsAndPolling();\n      break;\n\n     case \"ready\":\n      _zcSwfVersion = event.swfVersion;\n      var wasDeactivated = _flashState.deactivated === true;\n      _extend(_flashState, {\n        disabled: false,\n        outdated: false,\n        sandboxed: false,\n        unavailable: false,\n        degraded: false,\n        deactivated: false,\n        overdue: wasDeactivated,\n        ready: !wasDeactivated\n      });\n      _clearTimeoutsAndPolling();\n      break;\n\n     case \"beforecopy\":\n      _copyTarget = element;\n      break;\n\n     case \"copy\":\n      var textContent, htmlContent, targetEl = event.relatedTarget;\n      if (!(_clipData[\"text/html\"] || _clipData[\"text/plain\"]) && targetEl && (htmlContent = targetEl.value || targetEl.outerHTML || targetEl.innerHTML) && (textContent = targetEl.value || targetEl.textContent || targetEl.innerText)) {\n        event.clipboardData.clearData();\n        event.clipboardData.setData(\"text/plain\", textContent);\n        if (htmlContent !== textContent) {\n          event.clipboardData.setData(\"text/html\", htmlContent);\n        }\n      } else if (!_clipData[\"text/plain\"] && event.target && (textContent = event.target.getAttribute(\"data-clipboard-text\"))) {\n        event.clipboardData.clearData();\n        event.clipboardData.setData(\"text/plain\", textContent);\n      }\n      break;\n\n     case \"aftercopy\":\n      _queueEmitClipboardErrors(event);\n      ZeroClipboard.clearData();\n      if (element && element !== _safeActiveElement() && element.focus) {\n        element.focus();\n      }\n      break;\n\n     case \"_mouseover\":\n      ZeroClipboard.focus(element);\n      if (_globalConfig.bubbleEvents === true && sourceIsSwf) {\n        if (element && element !== event.relatedTarget && !_containedBy(event.relatedTarget, element)) {\n          _fireMouseEvent(_extend({}, event, {\n            type: \"mouseenter\",\n            bubbles: false,\n            cancelable: false\n          }));\n        }\n        _fireMouseEvent(_extend({}, event, {\n          type: \"mouseover\"\n        }));\n      }\n      break;\n\n     case \"_mouseout\":\n      ZeroClipboard.blur();\n      if (_globalConfig.bubbleEvents === true && sourceIsSwf) {\n        if (element && element !== event.relatedTarget && !_containedBy(event.relatedTarget, element)) {\n          _fireMouseEvent(_extend({}, event, {\n            type: \"mouseleave\",\n            bubbles: false,\n            cancelable: false\n          }));\n        }\n        _fireMouseEvent(_extend({}, event, {\n          type: \"mouseout\"\n        }));\n      }\n      break;\n\n     case \"_mousedown\":\n      _addClass(element, _globalConfig.activeClass);\n      if (_globalConfig.bubbleEvents === true && sourceIsSwf) {\n        _fireMouseEvent(_extend({}, event, {\n          type: event.type.slice(1)\n        }));\n      }\n      break;\n\n     case \"_mouseup\":\n      _removeClass(element, _globalConfig.activeClass);\n      if (_globalConfig.bubbleEvents === true && sourceIsSwf) {\n        _fireMouseEvent(_extend({}, event, {\n          type: event.type.slice(1)\n        }));\n      }\n      break;\n\n     case \"_click\":\n      _copyTarget = null;\n      if (_globalConfig.bubbleEvents === true && sourceIsSwf) {\n        _fireMouseEvent(_extend({}, event, {\n          type: event.type.slice(1)\n        }));\n      }\n      break;\n\n     case \"_mousemove\":\n      if (_globalConfig.bubbleEvents === true && sourceIsSwf) {\n        _fireMouseEvent(_extend({}, event, {\n          type: event.type.slice(1)\n        }));\n      }\n      break;\n    }\n    if (/^_(?:click|mouse(?:over|out|down|up|move))$/.test(event.type)) {\n      return true;\n    }\n  };\n  /**\n * Check an \"aftercopy\" event for clipboard errors and emit a corresponding \"error\" event.\n * @private\n */\n  var _queueEmitClipboardErrors = function(aftercopyEvent) {\n    if (aftercopyEvent.errors && aftercopyEvent.errors.length > 0) {\n      var errorEvent = _deepCopy(aftercopyEvent);\n      _extend(errorEvent, {\n        type: \"error\",\n        name: \"clipboard-error\"\n      });\n      delete errorEvent.success;\n      _setTimeout(function() {\n        ZeroClipboard.emit(errorEvent);\n      }, 0);\n    }\n  };\n  /**\n * Dispatch a synthetic MouseEvent.\n *\n * @returns `undefined`\n * @private\n */\n  var _fireMouseEvent = function(event) {\n    if (!(event && typeof event.type === \"string\" && event)) {\n      return;\n    }\n    var e, target = event.target || null, doc = target && target.ownerDocument || _document, defaults = {\n      view: doc.defaultView || _window,\n      canBubble: true,\n      cancelable: true,\n      detail: event.type === \"click\" ? 1 : 0,\n      button: typeof event.which === \"number\" ? event.which - 1 : typeof event.button === \"number\" ? event.button : doc.createEvent ? 0 : 1\n    }, args = _extend(defaults, event);\n    if (!target) {\n      return;\n    }\n    if (doc.createEvent && target.dispatchEvent) {\n      args = [ args.type, args.canBubble, args.cancelable, args.view, args.detail, args.screenX, args.screenY, args.clientX, args.clientY, args.ctrlKey, args.altKey, args.shiftKey, args.metaKey, args.button, args.relatedTarget ];\n      e = doc.createEvent(\"MouseEvents\");\n      if (e.initMouseEvent) {\n        e.initMouseEvent.apply(e, args);\n        e._source = \"js\";\n        target.dispatchEvent(e);\n      }\n    }\n  };\n  /**\n * Continuously poll the DOM until either:\n *  (a) the fallback content becomes visible, or\n *  (b) we receive an event from SWF (handled elsewhere)\n *\n * IMPORTANT:\n * This is NOT a necessary check but it can result in significantly faster\n * detection of bad `swfPath` configuration and/or network/server issues [in\n * supported browsers] than waiting for the entire `flashLoadTimeout` duration\n * to elapse before detecting that the SWF cannot be loaded. The detection\n * duration can be anywhere from 10-30 times faster [in supported browsers] by\n * using this approach.\n *\n * @returns `undefined`\n * @private\n */\n  var _watchForSwfFallbackContent = function() {\n    var maxWait = _globalConfig.flashLoadTimeout;\n    if (typeof maxWait === \"number\" && maxWait >= 0) {\n      var pollWait = Math.min(1e3, maxWait / 10);\n      var fallbackContentId = _globalConfig.swfObjectId + \"_fallbackContent\";\n      _swfFallbackCheckInterval = _setInterval(function() {\n        var el = _document.getElementById(fallbackContentId);\n        if (_isElementVisible(el)) {\n          _clearTimeoutsAndPolling();\n          _flashState.deactivated = null;\n          ZeroClipboard.emit({\n            type: \"error\",\n            name: \"swf-not-found\"\n          });\n        }\n      }, pollWait);\n    }\n  };\n  /**\n * Create the HTML bridge element to embed the Flash object into.\n * @private\n */\n  var _createHtmlBridge = function() {\n    var container = _document.createElement(\"div\");\n    container.id = _globalConfig.containerId;\n    container.className = _globalConfig.containerClass;\n    container.style.position = \"absolute\";\n    container.style.left = \"0px\";\n    container.style.top = \"-9999px\";\n    container.style.width = \"1px\";\n    container.style.height = \"1px\";\n    container.style.zIndex = \"\" + _getSafeZIndex(_globalConfig.zIndex);\n    return container;\n  };\n  /**\n * Get the HTML element container that wraps the Flash bridge object/element.\n * @private\n */\n  var _getHtmlBridge = function(flashBridge) {\n    var htmlBridge = flashBridge && flashBridge.parentNode;\n    while (htmlBridge && htmlBridge.nodeName === \"OBJECT\" && htmlBridge.parentNode) {\n      htmlBridge = htmlBridge.parentNode;\n    }\n    return htmlBridge || null;\n  };\n  /**\n * Create the SWF object.\n *\n * @returns The SWF object reference.\n * @private\n */\n  var _embedSwf = function() {\n    var len, flashBridge = _flashState.bridge, container = _getHtmlBridge(flashBridge);\n    if (!flashBridge) {\n      var allowScriptAccess = _determineScriptAccess(_window.location.host, _globalConfig);\n      var allowNetworking = allowScriptAccess === \"never\" ? \"none\" : \"all\";\n      var flashvars = _vars(_extend({\n        jsVersion: ZeroClipboard.version\n      }, _globalConfig));\n      var swfUrl = _globalConfig.swfPath + _cacheBust(_globalConfig.swfPath, _globalConfig);\n      container = _createHtmlBridge();\n      var divToBeReplaced = _document.createElement(\"div\");\n      container.appendChild(divToBeReplaced);\n      _document.body.appendChild(container);\n      var tmpDiv = _document.createElement(\"div\");\n      var usingActiveX = _flashState.pluginType === \"activex\";\n      tmpDiv.innerHTML = '<object id=\"' + _globalConfig.swfObjectId + '\" name=\"' + _globalConfig.swfObjectId + '\" ' + 'width=\"100%\" height=\"100%\" ' + (usingActiveX ? 'classid=\"clsid:d27cdb6e-ae6d-11cf-96b8-444553540000\"' : 'type=\"application/x-shockwave-flash\" data=\"' + swfUrl + '\"') + \">\" + (usingActiveX ? '<param name=\"movie\" value=\"' + swfUrl + '\"/>' : \"\") + '<param name=\"allowScriptAccess\" value=\"' + allowScriptAccess + '\"/>' + '<param name=\"allowNetworking\" value=\"' + allowNetworking + '\"/>' + '<param name=\"menu\" value=\"false\"/>' + '<param name=\"wmode\" value=\"transparent\"/>' + '<param name=\"flashvars\" value=\"' + flashvars + '\"/>' + '<div id=\"' + _globalConfig.swfObjectId + '_fallbackContent\">&nbsp;</div>' + \"</object>\";\n      flashBridge = tmpDiv.firstChild;\n      tmpDiv = null;\n      _unwrap(flashBridge).ZeroClipboard = ZeroClipboard;\n      container.replaceChild(flashBridge, divToBeReplaced);\n      _watchForSwfFallbackContent();\n    }\n    if (!flashBridge) {\n      flashBridge = _document[_globalConfig.swfObjectId];\n      if (flashBridge && (len = flashBridge.length)) {\n        flashBridge = flashBridge[len - 1];\n      }\n      if (!flashBridge && container) {\n        flashBridge = container.firstChild;\n      }\n    }\n    _flashState.bridge = flashBridge || null;\n    return flashBridge;\n  };\n  /**\n * Destroy the SWF object.\n * @private\n */\n  var _unembedSwf = function() {\n    var flashBridge = _flashState.bridge;\n    if (flashBridge) {\n      var htmlBridge = _getHtmlBridge(flashBridge);\n      if (htmlBridge) {\n        if (_flashState.pluginType === \"activex\" && \"readyState\" in flashBridge) {\n          flashBridge.style.display = \"none\";\n          (function removeSwfFromIE() {\n            if (flashBridge.readyState === 4) {\n              for (var prop in flashBridge) {\n                if (typeof flashBridge[prop] === \"function\") {\n                  flashBridge[prop] = null;\n                }\n              }\n              if (flashBridge.parentNode) {\n                flashBridge.parentNode.removeChild(flashBridge);\n              }\n              if (htmlBridge.parentNode) {\n                htmlBridge.parentNode.removeChild(htmlBridge);\n              }\n            } else {\n              _setTimeout(removeSwfFromIE, 10);\n            }\n          })();\n        } else {\n          if (flashBridge.parentNode) {\n            flashBridge.parentNode.removeChild(flashBridge);\n          }\n          if (htmlBridge.parentNode) {\n            htmlBridge.parentNode.removeChild(htmlBridge);\n          }\n        }\n      }\n      _clearTimeoutsAndPolling();\n      _flashState.ready = null;\n      _flashState.bridge = null;\n      _flashState.deactivated = null;\n      _zcSwfVersion = undefined;\n    }\n  };\n  /**\n * Map the data format names of the \"clipData\" to Flash-friendly names.\n *\n * @returns A new transformed object.\n * @private\n */\n  var _mapClipDataToFlash = function(clipData) {\n    var newClipData = {}, formatMap = {};\n    if (!(typeof clipData === \"object\" && clipData)) {\n      return;\n    }\n    for (var dataFormat in clipData) {\n      if (dataFormat && _hasOwn.call(clipData, dataFormat) && typeof clipData[dataFormat] === \"string\" && clipData[dataFormat]) {\n        switch (dataFormat.toLowerCase()) {\n         case \"text/plain\":\n         case \"text\":\n         case \"air:text\":\n         case \"flash:text\":\n          newClipData.text = clipData[dataFormat];\n          formatMap.text = dataFormat;\n          break;\n\n         case \"text/html\":\n         case \"html\":\n         case \"air:html\":\n         case \"flash:html\":\n          newClipData.html = clipData[dataFormat];\n          formatMap.html = dataFormat;\n          break;\n\n         case \"application/rtf\":\n         case \"text/rtf\":\n         case \"rtf\":\n         case \"richtext\":\n         case \"air:rtf\":\n         case \"flash:rtf\":\n          newClipData.rtf = clipData[dataFormat];\n          formatMap.rtf = dataFormat;\n          break;\n\n         default:\n          break;\n        }\n      }\n    }\n    return {\n      data: newClipData,\n      formatMap: formatMap\n    };\n  };\n  /**\n * Map the data format names from Flash-friendly names back to their original \"clipData\" names (via a format mapping).\n *\n * @returns A new transformed object.\n * @private\n */\n  var _mapClipResultsFromFlash = function(clipResults, formatMap) {\n    if (!(typeof clipResults === \"object\" && clipResults && typeof formatMap === \"object\" && formatMap)) {\n      return clipResults;\n    }\n    var newResults = {};\n    for (var prop in clipResults) {\n      if (_hasOwn.call(clipResults, prop)) {\n        if (prop === \"errors\") {\n          newResults[prop] = clipResults[prop] ? clipResults[prop].slice() : [];\n          for (var i = 0, len = newResults[prop].length; i < len; i++) {\n            newResults[prop][i].format = formatMap[newResults[prop][i].format];\n          }\n        } else if (prop !== \"success\" && prop !== \"data\") {\n          newResults[prop] = clipResults[prop];\n        } else {\n          newResults[prop] = {};\n          var tmpHash = clipResults[prop];\n          for (var dataFormat in tmpHash) {\n            if (dataFormat && _hasOwn.call(tmpHash, dataFormat) && _hasOwn.call(formatMap, dataFormat)) {\n              newResults[prop][formatMap[dataFormat]] = tmpHash[dataFormat];\n            }\n          }\n        }\n      }\n    }\n    return newResults;\n  };\n  /**\n * Will look at a path, and will create a \"?noCache={time}\" or \"&noCache={time}\"\n * query param string to return. Does NOT append that string to the original path.\n * This is useful because ExternalInterface often breaks when a Flash SWF is cached.\n *\n * @returns The `noCache` query param with necessary \"?\"/\"&\" prefix.\n * @private\n */\n  var _cacheBust = function(path, options) {\n    var cacheBust = options == null || options && options.cacheBust === true;\n    if (cacheBust) {\n      return (path.indexOf(\"?\") === -1 ? \"?\" : \"&\") + \"noCache=\" + _now();\n    } else {\n      return \"\";\n    }\n  };\n  /**\n * Creates a query string for the FlashVars param.\n * Does NOT include the cache-busting query param.\n *\n * @returns FlashVars query string\n * @private\n */\n  var _vars = function(options) {\n    var i, len, domain, domains, str = \"\", trustedOriginsExpanded = [];\n    if (options.trustedDomains) {\n      if (typeof options.trustedDomains === \"string\") {\n        domains = [ options.trustedDomains ];\n      } else if (typeof options.trustedDomains === \"object\" && \"length\" in options.trustedDomains) {\n        domains = options.trustedDomains;\n      }\n    }\n    if (domains && domains.length) {\n      for (i = 0, len = domains.length; i < len; i++) {\n        if (_hasOwn.call(domains, i) && domains[i] && typeof domains[i] === \"string\") {\n          domain = _extractDomain(domains[i]);\n          if (!domain) {\n            continue;\n          }\n          if (domain === \"*\") {\n            trustedOriginsExpanded.length = 0;\n            trustedOriginsExpanded.push(domain);\n            break;\n          }\n          trustedOriginsExpanded.push.apply(trustedOriginsExpanded, [ domain, \"//\" + domain, _window.location.protocol + \"//\" + domain ]);\n        }\n      }\n    }\n    if (trustedOriginsExpanded.length) {\n      str += \"trustedOrigins=\" + _encodeURIComponent(trustedOriginsExpanded.join(\",\"));\n    }\n    if (options.forceEnhancedClipboard === true) {\n      str += (str ? \"&\" : \"\") + \"forceEnhancedClipboard=true\";\n    }\n    if (typeof options.swfObjectId === \"string\" && options.swfObjectId) {\n      str += (str ? \"&\" : \"\") + \"swfObjectId=\" + _encodeURIComponent(options.swfObjectId);\n    }\n    if (typeof options.jsVersion === \"string\" && options.jsVersion) {\n      str += (str ? \"&\" : \"\") + \"jsVersion=\" + _encodeURIComponent(options.jsVersion);\n    }\n    return str;\n  };\n  /**\n * Extract the domain (e.g. \"github.com\") from an origin (e.g. \"https://github.com\") or\n * URL (e.g. \"https://github.com/zeroclipboard/zeroclipboard/\").\n *\n * @returns the domain\n * @private\n */\n  var _extractDomain = function(originOrUrl) {\n    if (originOrUrl == null || originOrUrl === \"\") {\n      return null;\n    }\n    originOrUrl = originOrUrl.replace(/^\\s+|\\s+$/g, \"\");\n    if (originOrUrl === \"\") {\n      return null;\n    }\n    var protocolIndex = originOrUrl.indexOf(\"//\");\n    originOrUrl = protocolIndex === -1 ? originOrUrl : originOrUrl.slice(protocolIndex + 2);\n    var pathIndex = originOrUrl.indexOf(\"/\");\n    originOrUrl = pathIndex === -1 ? originOrUrl : protocolIndex === -1 || pathIndex === 0 ? null : originOrUrl.slice(0, pathIndex);\n    if (originOrUrl && originOrUrl.slice(-4).toLowerCase() === \".swf\") {\n      return null;\n    }\n    return originOrUrl || null;\n  };\n  /**\n * Set `allowScriptAccess` based on `trustedDomains` and `window.location.host` vs. `swfPath`.\n *\n * @returns The appropriate script access level.\n * @private\n */\n  var _determineScriptAccess = function() {\n    var _extractAllDomains = function(origins) {\n      var i, len, tmp, resultsArray = [];\n      if (typeof origins === \"string\") {\n        origins = [ origins ];\n      }\n      if (!(typeof origins === \"object\" && origins && typeof origins.length === \"number\")) {\n        return resultsArray;\n      }\n      for (i = 0, len = origins.length; i < len; i++) {\n        if (_hasOwn.call(origins, i) && (tmp = _extractDomain(origins[i]))) {\n          if (tmp === \"*\") {\n            resultsArray.length = 0;\n            resultsArray.push(\"*\");\n            break;\n          }\n          if (resultsArray.indexOf(tmp) === -1) {\n            resultsArray.push(tmp);\n          }\n        }\n      }\n      return resultsArray;\n    };\n    return function(currentDomain, configOptions) {\n      var swfDomain = _extractDomain(configOptions.swfPath);\n      if (swfDomain === null) {\n        swfDomain = currentDomain;\n      }\n      var trustedDomains = _extractAllDomains(configOptions.trustedDomains);\n      var len = trustedDomains.length;\n      if (len > 0) {\n        if (len === 1 && trustedDomains[0] === \"*\") {\n          return \"always\";\n        }\n        if (trustedDomains.indexOf(currentDomain) !== -1) {\n          if (len === 1 && currentDomain === swfDomain) {\n            return \"sameDomain\";\n          }\n          return \"always\";\n        }\n      }\n      return \"never\";\n    };\n  }();\n  /**\n * Get the currently active/focused DOM element.\n *\n * @returns the currently active/focused element, or `null`\n * @private\n */\n  var _safeActiveElement = function() {\n    try {\n      return _document.activeElement;\n    } catch (err) {\n      return null;\n    }\n  };\n  /**\n * Add a class to an element, if it doesn't already have it.\n *\n * @returns The element, with its new class added.\n * @private\n */\n  var _addClass = function(element, value) {\n    var c, cl, className, classNames = [];\n    if (typeof value === \"string\" && value) {\n      classNames = value.split(/\\s+/);\n    }\n    if (element && element.nodeType === 1 && classNames.length > 0) {\n      if (element.classList) {\n        for (c = 0, cl = classNames.length; c < cl; c++) {\n          element.classList.add(classNames[c]);\n        }\n      } else if (element.hasOwnProperty(\"className\")) {\n        className = \" \" + element.className + \" \";\n        for (c = 0, cl = classNames.length; c < cl; c++) {\n          if (className.indexOf(\" \" + classNames[c] + \" \") === -1) {\n            className += classNames[c] + \" \";\n          }\n        }\n        element.className = className.replace(/^\\s+|\\s+$/g, \"\");\n      }\n    }\n    return element;\n  };\n  /**\n * Remove a class from an element, if it has it.\n *\n * @returns The element, with its class removed.\n * @private\n */\n  var _removeClass = function(element, value) {\n    var c, cl, className, classNames = [];\n    if (typeof value === \"string\" && value) {\n      classNames = value.split(/\\s+/);\n    }\n    if (element && element.nodeType === 1 && classNames.length > 0) {\n      if (element.classList && element.classList.length > 0) {\n        for (c = 0, cl = classNames.length; c < cl; c++) {\n          element.classList.remove(classNames[c]);\n        }\n      } else if (element.className) {\n        className = (\" \" + element.className + \" \").replace(/[\\r\\n\\t]/g, \" \");\n        for (c = 0, cl = classNames.length; c < cl; c++) {\n          className = className.replace(\" \" + classNames[c] + \" \", \" \");\n        }\n        element.className = className.replace(/^\\s+|\\s+$/g, \"\");\n      }\n    }\n    return element;\n  };\n  /**\n * Attempt to interpret the element's CSS styling. If `prop` is `\"cursor\"`,\n * then we assume that it should be a hand (\"pointer\") cursor if the element\n * is an anchor element (\"a\" tag).\n *\n * @returns The computed style property.\n * @private\n */\n  var _getStyle = function(el, prop) {\n    var value = _getComputedStyle(el, null).getPropertyValue(prop);\n    if (prop === \"cursor\") {\n      if (!value || value === \"auto\") {\n        if (el.nodeName === \"A\") {\n          return \"pointer\";\n        }\n      }\n    }\n    return value;\n  };\n  /**\n * Get the absolutely positioned coordinates of a DOM element.\n *\n * @returns Object containing the element's position, width, and height.\n * @private\n */\n  var _getElementPosition = function(el) {\n    var pos = {\n      left: 0,\n      top: 0,\n      width: 0,\n      height: 0\n    };\n    if (el.getBoundingClientRect) {\n      var elRect = el.getBoundingClientRect();\n      var pageXOffset = _window.pageXOffset;\n      var pageYOffset = _window.pageYOffset;\n      var leftBorderWidth = _document.documentElement.clientLeft || 0;\n      var topBorderWidth = _document.documentElement.clientTop || 0;\n      var leftBodyOffset = 0;\n      var topBodyOffset = 0;\n      if (_getStyle(_document.body, \"position\") === \"relative\") {\n        var bodyRect = _document.body.getBoundingClientRect();\n        var htmlRect = _document.documentElement.getBoundingClientRect();\n        leftBodyOffset = bodyRect.left - htmlRect.left || 0;\n        topBodyOffset = bodyRect.top - htmlRect.top || 0;\n      }\n      pos.left = elRect.left + pageXOffset - leftBorderWidth - leftBodyOffset;\n      pos.top = elRect.top + pageYOffset - topBorderWidth - topBodyOffset;\n      pos.width = \"width\" in elRect ? elRect.width : elRect.right - elRect.left;\n      pos.height = \"height\" in elRect ? elRect.height : elRect.bottom - elRect.top;\n    }\n    return pos;\n  };\n  /**\n * Determine is an element is visible somewhere within the document (page).\n *\n * @returns Boolean\n * @private\n */\n  var _isElementVisible = function(el) {\n    if (!el) {\n      return false;\n    }\n    var styles = _getComputedStyle(el, null);\n    var hasCssHeight = _parseFloat(styles.height) > 0;\n    var hasCssWidth = _parseFloat(styles.width) > 0;\n    var hasCssTop = _parseFloat(styles.top) >= 0;\n    var hasCssLeft = _parseFloat(styles.left) >= 0;\n    var cssKnows = hasCssHeight && hasCssWidth && hasCssTop && hasCssLeft;\n    var rect = cssKnows ? null : _getElementPosition(el);\n    var isVisible = styles.display !== \"none\" && styles.visibility !== \"collapse\" && (cssKnows || !!rect && (hasCssHeight || rect.height > 0) && (hasCssWidth || rect.width > 0) && (hasCssTop || rect.top >= 0) && (hasCssLeft || rect.left >= 0));\n    return isVisible;\n  };\n  /**\n * Clear all existing timeouts and interval polling delegates.\n *\n * @returns `undefined`\n * @private\n */\n  var _clearTimeoutsAndPolling = function() {\n    _clearTimeout(_flashCheckTimeout);\n    _flashCheckTimeout = 0;\n    _clearInterval(_swfFallbackCheckInterval);\n    _swfFallbackCheckInterval = 0;\n  };\n  /**\n * Reposition the Flash object to cover the currently activated element.\n *\n * @returns `undefined`\n * @private\n */\n  var _reposition = function() {\n    var htmlBridge;\n    if (_currentElement && (htmlBridge = _getHtmlBridge(_flashState.bridge))) {\n      var pos = _getElementPosition(_currentElement);\n      _extend(htmlBridge.style, {\n        width: pos.width + \"px\",\n        height: pos.height + \"px\",\n        top: pos.top + \"px\",\n        left: pos.left + \"px\",\n        zIndex: \"\" + _getSafeZIndex(_globalConfig.zIndex)\n      });\n    }\n  };\n  /**\n * Sends a signal to the Flash object to display the hand cursor if `true`.\n *\n * @returns `undefined`\n * @private\n */\n  var _setHandCursor = function(enabled) {\n    if (_flashState.ready === true) {\n      if (_flashState.bridge && typeof _flashState.bridge.setHandCursor === \"function\") {\n        _flashState.bridge.setHandCursor(enabled);\n      } else {\n        _flashState.ready = false;\n      }\n    }\n  };\n  /**\n * Get a safe value for `zIndex`\n *\n * @returns an integer, or \"auto\"\n * @private\n */\n  var _getSafeZIndex = function(val) {\n    if (/^(?:auto|inherit)$/.test(val)) {\n      return val;\n    }\n    var zIndex;\n    if (typeof val === \"number\" && !_isNaN(val)) {\n      zIndex = val;\n    } else if (typeof val === \"string\") {\n      zIndex = _getSafeZIndex(_parseInt(val, 10));\n    }\n    return typeof zIndex === \"number\" ? zIndex : \"auto\";\n  };\n  /**\n * Attempt to detect if ZeroClipboard is executing inside of a sandboxed iframe.\n * If it is, Flash Player cannot be used, so ZeroClipboard is dead in the water.\n *\n * @see {@link http://lists.w3.org/Archives/Public/public-whatwg-archive/2014Dec/0002.html}\n * @see {@link https://github.com/zeroclipboard/zeroclipboard/issues/511}\n * @see {@link http://zeroclipboard.org/test-iframes.html}\n *\n * @returns `true` (is sandboxed), `false` (is not sandboxed), or `null` (uncertain) \n * @private\n */\n  var _detectSandbox = function(doNotReassessFlashSupport) {\n    var effectiveScriptOrigin, frame, frameError, previousState = _flashState.sandboxed, isSandboxed = null;\n    doNotReassessFlashSupport = doNotReassessFlashSupport === true;\n    if (_pageIsFramed === false) {\n      isSandboxed = false;\n    } else {\n      try {\n        frame = window.frameElement || null;\n      } catch (e) {\n        frameError = {\n          name: e.name,\n          message: e.message\n        };\n      }\n      if (frame && frame.nodeType === 1 && frame.nodeName === \"IFRAME\") {\n        try {\n          isSandboxed = frame.hasAttribute(\"sandbox\");\n        } catch (e) {\n          isSandboxed = null;\n        }\n      } else {\n        try {\n          effectiveScriptOrigin = document.domain || null;\n        } catch (e) {\n          effectiveScriptOrigin = null;\n        }\n        if (effectiveScriptOrigin === null || frameError && frameError.name === \"SecurityError\" && /(^|[\\s\\(\\[@])sandbox(es|ed|ing|[\\s\\.,!\\)\\]@]|$)/.test(frameError.message.toLowerCase())) {\n          isSandboxed = true;\n        }\n      }\n    }\n    _flashState.sandboxed = isSandboxed;\n    if (previousState !== isSandboxed && !doNotReassessFlashSupport) {\n      _detectFlashSupport(_ActiveXObject);\n    }\n    return isSandboxed;\n  };\n  /**\n * Detect the Flash Player status, version, and plugin type.\n *\n * @see {@link https://code.google.com/p/doctype-mirror/wiki/ArticleDetectFlash#The_code}\n * @see {@link http://stackoverflow.com/questions/12866060/detecting-pepper-ppapi-flash-with-javascript}\n *\n * @returns `undefined`\n * @private\n */\n  var _detectFlashSupport = function(ActiveXObject) {\n    var plugin, ax, mimeType, hasFlash = false, isActiveX = false, isPPAPI = false, flashVersion = \"\";\n    /**\n   * Derived from Apple's suggested sniffer.\n   * @param {String} desc e.g. \"Shockwave Flash 7.0 r61\"\n   * @returns {String} \"7.0.61\"\n   * @private\n   */\n    function parseFlashVersion(desc) {\n      var matches = desc.match(/[\\d]+/g);\n      matches.length = 3;\n      return matches.join(\".\");\n    }\n    function isPepperFlash(flashPlayerFileName) {\n      return !!flashPlayerFileName && (flashPlayerFileName = flashPlayerFileName.toLowerCase()) && (/^(pepflashplayer\\.dll|libpepflashplayer\\.so|pepperflashplayer\\.plugin)$/.test(flashPlayerFileName) || flashPlayerFileName.slice(-13) === \"chrome.plugin\");\n    }\n    function inspectPlugin(plugin) {\n      if (plugin) {\n        hasFlash = true;\n        if (plugin.version) {\n          flashVersion = parseFlashVersion(plugin.version);\n        }\n        if (!flashVersion && plugin.description) {\n          flashVersion = parseFlashVersion(plugin.description);\n        }\n        if (plugin.filename) {\n          isPPAPI = isPepperFlash(plugin.filename);\n        }\n      }\n    }\n    if (_navigator.plugins && _navigator.plugins.length) {\n      plugin = _navigator.plugins[\"Shockwave Flash\"];\n      inspectPlugin(plugin);\n      if (_navigator.plugins[\"Shockwave Flash 2.0\"]) {\n        hasFlash = true;\n        flashVersion = \"********\";\n      }\n    } else if (_navigator.mimeTypes && _navigator.mimeTypes.length) {\n      mimeType = _navigator.mimeTypes[\"application/x-shockwave-flash\"];\n      plugin = mimeType && mimeType.enabledPlugin;\n      inspectPlugin(plugin);\n    } else if (typeof ActiveXObject !== \"undefined\") {\n      isActiveX = true;\n      try {\n        ax = new ActiveXObject(\"ShockwaveFlash.ShockwaveFlash.7\");\n        hasFlash = true;\n        flashVersion = parseFlashVersion(ax.GetVariable(\"$version\"));\n      } catch (e1) {\n        try {\n          ax = new ActiveXObject(\"ShockwaveFlash.ShockwaveFlash.6\");\n          hasFlash = true;\n          flashVersion = \"6.0.21\";\n        } catch (e2) {\n          try {\n            ax = new ActiveXObject(\"ShockwaveFlash.ShockwaveFlash\");\n            hasFlash = true;\n            flashVersion = parseFlashVersion(ax.GetVariable(\"$version\"));\n          } catch (e3) {\n            isActiveX = false;\n          }\n        }\n      }\n    }\n    _flashState.disabled = hasFlash !== true;\n    _flashState.outdated = flashVersion && _parseFloat(flashVersion) < _parseFloat(_minimumFlashVersion);\n    _flashState.version = flashVersion || \"0.0.0\";\n    _flashState.pluginType = isPPAPI ? \"pepper\" : isActiveX ? \"activex\" : hasFlash ? \"netscape\" : \"unknown\";\n  };\n  /**\n * Invoke the Flash detection algorithms immediately upon inclusion so we're not waiting later.\n */\n  _detectFlashSupport(_ActiveXObject);\n  /**\n * Always assess the `sandboxed` state of the page at important Flash-related moments.\n */\n  _detectSandbox(true);\n  /**\n * A shell constructor for `ZeroClipboard` client instances.\n *\n * @constructor\n */\n  var ZeroClipboard = function() {\n    if (!(this instanceof ZeroClipboard)) {\n      return new ZeroClipboard();\n    }\n    if (typeof ZeroClipboard._createClient === \"function\") {\n      ZeroClipboard._createClient.apply(this, _args(arguments));\n    }\n  };\n  /**\n * The ZeroClipboard library's version number.\n *\n * @static\n * @readonly\n * @property {string}\n */\n  _defineProperty(ZeroClipboard, \"version\", {\n    value: \"2.2.0\",\n    writable: false,\n    configurable: true,\n    enumerable: true\n  });\n  /**\n * Update or get a copy of the ZeroClipboard global configuration.\n * Returns a copy of the current/updated configuration.\n *\n * @returns Object\n * @static\n */\n  ZeroClipboard.config = function() {\n    return _config.apply(this, _args(arguments));\n  };\n  /**\n * Diagnostic method that describes the state of the browser, Flash Player, and ZeroClipboard.\n *\n * @returns Object\n * @static\n */\n  ZeroClipboard.state = function() {\n    return _state.apply(this, _args(arguments));\n  };\n  /**\n * Check if Flash is unusable for any reason: disabled, outdated, deactivated, etc.\n *\n * @returns Boolean\n * @static\n */\n  ZeroClipboard.isFlashUnusable = function() {\n    return _isFlashUnusable.apply(this, _args(arguments));\n  };\n  /**\n * Register an event listener.\n *\n * @returns `ZeroClipboard`\n * @static\n */\n  ZeroClipboard.on = function() {\n    return _on.apply(this, _args(arguments));\n  };\n  /**\n * Unregister an event listener.\n * If no `listener` function/object is provided, it will unregister all listeners for the provided `eventType`.\n * If no `eventType` is provided, it will unregister all listeners for every event type.\n *\n * @returns `ZeroClipboard`\n * @static\n */\n  ZeroClipboard.off = function() {\n    return _off.apply(this, _args(arguments));\n  };\n  /**\n * Retrieve event listeners for an `eventType`.\n * If no `eventType` is provided, it will retrieve all listeners for every event type.\n *\n * @returns array of listeners for the `eventType`; if no `eventType`, then a map/hash object of listeners for all event types; or `null`\n */\n  ZeroClipboard.handlers = function() {\n    return _listeners.apply(this, _args(arguments));\n  };\n  /**\n * Event emission receiver from the Flash object, forwarding to any registered JavaScript event listeners.\n *\n * @returns For the \"copy\" event, returns the Flash-friendly \"clipData\" object; otherwise `undefined`.\n * @static\n */\n  ZeroClipboard.emit = function() {\n    return _emit.apply(this, _args(arguments));\n  };\n  /**\n * Create and embed the Flash object.\n *\n * @returns The Flash object\n * @static\n */\n  ZeroClipboard.create = function() {\n    return _create.apply(this, _args(arguments));\n  };\n  /**\n * Self-destruct and clean up everything, including the embedded Flash object.\n *\n * @returns `undefined`\n * @static\n */\n  ZeroClipboard.destroy = function() {\n    return _destroy.apply(this, _args(arguments));\n  };\n  /**\n * Set the pending data for clipboard injection.\n *\n * @returns `undefined`\n * @static\n */\n  ZeroClipboard.setData = function() {\n    return _setData.apply(this, _args(arguments));\n  };\n  /**\n * Clear the pending data for clipboard injection.\n * If no `format` is provided, all pending data formats will be cleared.\n *\n * @returns `undefined`\n * @static\n */\n  ZeroClipboard.clearData = function() {\n    return _clearData.apply(this, _args(arguments));\n  };\n  /**\n * Get a copy of the pending data for clipboard injection.\n * If no `format` is provided, a copy of ALL pending data formats will be returned.\n *\n * @returns `String` or `Object`\n * @static\n */\n  ZeroClipboard.getData = function() {\n    return _getData.apply(this, _args(arguments));\n  };\n  /**\n * Sets the current HTML object that the Flash object should overlay. This will put the global\n * Flash object on top of the current element; depending on the setup, this may also set the\n * pending clipboard text data as well as the Flash object's wrapping element's title attribute\n * based on the underlying HTML element and ZeroClipboard configuration.\n *\n * @returns `undefined`\n * @static\n */\n  ZeroClipboard.focus = ZeroClipboard.activate = function() {\n    return _focus.apply(this, _args(arguments));\n  };\n  /**\n * Un-overlays the Flash object. This will put the global Flash object off-screen; depending on\n * the setup, this may also unset the Flash object's wrapping element's title attribute based on\n * the underlying HTML element and ZeroClipboard configuration.\n *\n * @returns `undefined`\n * @static\n */\n  ZeroClipboard.blur = ZeroClipboard.deactivate = function() {\n    return _blur.apply(this, _args(arguments));\n  };\n  /**\n * Returns the currently focused/\"activated\" HTML element that the Flash object is wrapping.\n *\n * @returns `HTMLElement` or `null`\n * @static\n */\n  ZeroClipboard.activeElement = function() {\n    return _activeElement.apply(this, _args(arguments));\n  };\n  if (typeof define === \"function\" && define.amd) {\n    define(function() {\n      return ZeroClipboard;\n    });\n  } else if (typeof module === \"object\" && module && typeof module.exports === \"object\" && module.exports) {\n    module.exports = ZeroClipboard;\n  } else {\n    window.ZeroClipboard = ZeroClipboard;\n  }\n})(function() {\n  return this || window;\n}());"]}