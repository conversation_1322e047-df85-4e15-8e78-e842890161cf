html {
    background-color: #F2F2F2;
}

body {
    max-width: 700px;
    margin: auto;
    padding-top: 90px;
    padding-bottom: 20px;
    padding-left: 4px;
    padding-right: 4px;
}

#header {
    position: fixed;
    top: 0px;
    left:0px;
    width: 100%;
    background-color: #2C3E50;
    cursor: pointer;
    padding: 10px 10px;
    font-family: sans-serif;
    font-size: 16px;
    color: #F80B6D;
    text-align: center;
    z-index: 999;
}

.fileDiv {
    border: solid 1px #E6E7E6;
    background-color: #FFF;
    border-radius: 1px;
    width: 30%;
    float: left;
    height: 173px;
    margin-right: 1%;
    margin-left: 1%;
    margin-bottom: 2.4%;
    overflow: hidden;
    cursor: pointer;
    
    -webkit-box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.05);
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.05);
    
		transition: all .16s ease-in-out;
    -moz-transition: all .16s ease-in-out;
		-webkit-transition: all .16s ease-in-out;
}

.fileDiv:hover {
    transform: scale(1.05);
    -moz-transform: scale(1.05);
		-webkit-transform: scale(1.05);
}

.fileDiv:active {
    transform: scale(1.03);
		-webkit-transform: scale(1.03);
}

.selected {
    background-color: #81CFE0;
}

.selected:hover {
    background-color: #81CFE0 !important;
}

@media only all and (min-width: 615px) {
    .fileDiv {
        width: 31%;
    }
}

@media only all and (max-width: 615px) {
    .fileDiv {
        width: 30.5%;
    }
}

@media only all and (max-width: 450px) {
    .fileDiv {
        width: 47%;
    }
}

@media only all and (max-width: 367px) {
    .fileDiv {
        width: 46%;
    }
}

@media only all and (max-width: 270px) {
    .fileDiv {
        width: 95%;
    }
}

.imgDiv {
    height: 120px;
    width: 100%;
    overflow: hidden;
    border-bottom: solid 1px #EAEAEA;
    text-align: center;
    padding-bottom: 5px;
    font-family: sans-serif;
    font-size: 38px;
    font-weight: bold;
}

.fileImg {
    width: auto;
    height: 100%;
    margin-bottom: 0px;
}

.fileDescription {
    font-family: sans-serif;
    font-size: 13px;
    text-align: left;
    padding-left: 4px;
    margin-bottom: 2px;
    margin-top: 5px;
}

.fileTime {
    font-family: sans-serif;
    font-size: 10px;
    text-align: left;
    padding-left: 4px;
    margin-top: 0px;
    margin-bottom: 0px;
}

#imageFullSreen {
}

#imageFSimg {
    width: 100%;
    max-width: 695px;
    margin-top: 16px;
    margin-bottom: 5px;
}

.imgActionP {
    display: inline-block;
    width: 48%;
    max-width: 345.5px;
    cursor: pointer;
    font-family: sans-serif;
    font-size: 16px;
    font-weight: bold;
}

.background {
    background-color: #F2F2F2;
    opacity: 0.98;
    z-index: 1000;
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0px;
    right: 0px;
    top: 0px;
    bottom: 0px;
    display: none;
    cursor: pointer;
}

.headerBtn {
    border: none;
    background-color: #E0EAF1;
    border-radius: 5px;
    padding: 3px 3px;
    cursor: pointer;
    font-size: 15px;
    color: #0077CC;
    margin-right: 3px;
    margin-top: 6px;
}

.redBtn {
    background-color: #F2DEDE;
    color: #A94442;
}

.greyBtn {
    background-color: #F1F1F1;
    color: #171B1F;
}

.greenBtn {
    background-color: #DFF0D8;
    color: #3C763D;
}

.headerBtn:hover {
    background-color: #0077CC;
    color: #fff;
}

.redBtn:hover {
    background-color: #A94442;
    color: #fff;
}

.greyBtn:hover {
    background-color: #818185;
    color: #F1F1F1;
}

.greenBtn:hover {
    background-color: #3C763D;
    color: #fff;
}

.headerA {
    font-weight: lighter;
    color: #222222;
    background-color: #FFFFFF;
    text-decoration: none;
    font-family: "Open Sans",sans-serif;
    font-size: 22px;
}

.headerA:hover {
    color: #FFFFFF;
    background-color: #F90B6D;
}

.buttonBar {
    position: fixed;
    text-align: left;
    max-width: 695px;
    background-color: #FFFFFF;
    padding-bottom: 7px;
    padding-top: 6px;
    opacity: 0.9;
}

#uploadImgDiv {
}

.uploadP {
    font-weight: lighter;
    color: #222222;
    background-color: #FFFFFF;
    text-decoration: none;
    font-family: "Open Sans",sans-serif;
    font-size: 18px;
    max-width: 200px;
    cursor: pointer;
    margin: auto;
    margin-bottom: 8px;
    margin-top: 10px;
}

.uploadP:hover {
    color: #FFFFFF;
    background-color: #F90B6D;
}

input {
    cursor: pointer;
}

editable:hover {
    color: #FFFFFF;
    background-color: #F90B6D;
}

#updates {
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 14px;
    font-weight: inherit;
    padding: 15px 20px 15px 42px;
    border-radius: 1px;
    margin-bottom: 16px;
    margin-top: -16px;
    color: #2C3E50;
    display: none;
    
    background: #FFFFFF url('img/cd-icon-updates.png') no-repeat center left 12px;	
		background-size: 22px 22px;		
    
    -webkit-box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.2), 0px 1px 3px 0px rgba(0, 0, 0, 0.05), 0px 1px 0px rgba(255, 255, 255, 0.25) inset;
    -moz-box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.2), 0px 1px 3px 0px rgba(0, 0, 0, 0.05), 0px 1px 0px rgba(255, 255, 255, 0.25) inset;
    box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.2), 0px 1px 3px 0px rgba(0, 0, 0, 0.05), 0px 1px 0px rgba(255, 255, 255, 0.25) inset;
}

#updates a {
    font-weight: bold;
    text-decoration: none;
    border-radius: 0px;
    background-color: transparent;
    border-bottom: 2px solid #C1DDF5;
    box-shadow: 0px -6px 0px #C1DDF5 inset;
    color: inherit;
}

#updates a:hover {
    background-color: #C1DDF5;
}

.folderInfo {
    padding 12px;
    margin-right: 1.2%;
    margin-left: 1.2%;
    margin-bottom: 2.4%;
    overflow: hidden;
    cursor: pointer;
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 14px;
    font-weight: bold;
}

.headerIcon {
    height: 18px;
    vertical-align: text-bottom;
}

.headerIconLogo {
    height: 35px;
    vertical-align: text-bottom;
    float: left;
    margin-left: 4px;
}

.headerIconCenter {
    height: 29px;
    float: left;
    margin-left: 20px;
    margin-top: 4px;
}

.headerIconRight {
    height: 20px;
    float: right;
    margin-right: 25px;
    margin-top: 7px;
}

.iconHover:hover {
    -webkit-filter: brightness(8);
    filter: brightness(8);
}
    
#settingsDiv {
}

.settingsh3 {
    font-weight: bolder;
    color: #222222;
    text-decoration: none;
    font-family: "Open Sans",sans-serif;
    font-size: 14px;
    max-width: 200px;
    cursor: pointer;
    margin: auto;
    margin-bottom: 8px;
    margin-top: 10px;
}

.saveUploadPathA {
    margin-top: 0px;
    display: none;
}

.editableActive {
    color: #222222 !important;
    background-color: #FFFFFF !important;
    cursor: inherit !important;
}

#folderError {
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 14px;
    font-weight: inherit;
    padding: 15px 20px 15px 42px;
    border-radius: 1px;
    margin-bottom: 16px;
    margin-top: -5px;
    color: #2C3E50;
    
    background: #FFFFFF url('img/cd-icon-warning.png') no-repeat center left 12px;	
		background-size: 22px 22px;		
    
    -webkit-box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.2), 0px 1px 3px 0px rgba(0, 0, 0, 0.05), 0px 1px 0px rgba(255, 255, 255, 0.25) inset;
    -moz-box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.2), 0px 1px 3px 0px rgba(0, 0, 0, 0.05), 0px 1px 0px rgba(255, 255, 255, 0.25) inset;
    box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.2), 0px 1px 3px 0px rgba(0, 0, 0, 0.05), 0px 1px 0px rgba(255, 255, 255, 0.25) inset;
}

#folderError button {
    padding: 0px 2px;
    border-radius: 0px;
    background-color: transparent;
    border-bottom: 2px solid #C1DDF5;
    box-shadow: 0px -6px 0px #C1DDF5 inset;
    color: inherit;
}

#folderError button:hover {
    background-color: #C1DDF5;
}

.saveUploadPathP {
    font-weight: lighter;
    display: none;
    margin-bottom: -5px;
}

.pathHistory {
    color: #222222;
    text-decoration: none;
    font-family: "Open Sans",sans-serif;
    font-size: 13px;
    max-width: 240px;
    cursor: pointer;
    margin: auto;
    margin-bottom: 8px;
    margin-top: 10px;
    font-weight: lighter;
    margin-bottom: -5px;
    display: none;
}

.pathHistory:hover {
    color: #55ACEE;
}

.saveUploadPathA {
    margin-top: 18px;
}

.fileMime {
	display:inline-block;
	border:solid;
	border-width:thin;
	border-color:#404040;
	padding: 0px 1px;
	font-size:9px;
	font-family:Verdana, Geneva, sans-serif;
	text-transform:uppercase;
	font-weight:bolder;
	color:#404040;
}

.editIcon {
    width: 15px;
    float: right;
    margin-right: 4px;
}

.fullWidthFileDiv {
    width: 100%;
    cursor: pointer;
}

.fullWidthFileDiv:hover {
    background-color: #E0EAF1;
}

.fullWidthfileImg {
    height: 16px;
}

.fullWidthimgDiv {
    display: inline-block;
    width: 30px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    margin-bottom: 12px;
    text-align: left;
}

.fullWidthfileDescription {
    font-family: sans-serif;
    font-size: 15px;
    text-align: left;
    font-weight: 600;
    display: inline-block;
    max-width: 25%;
    width: 25%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.fullWidthfileTime {
    font-family: sans-serif;
    font-size: 15px;
    font-weight: 300;
    text-align: center;
    display: inline-block;
    float: right;
    margin-left: 8%;
    max-width: 9%;
    width: 9%;
    max-height: 16px;
    overflow-y: hidden;
}

.fullWidth30percent {
    max-width: 30%;
    width: 27%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

@media only all and (max-width: 580px) {
    .fullWidthfileTime {
        margin-left: 2%;
        max-width: 12%;
        width: 12%;
    }
    .fullWidth30percent {
        max-width: 20%;
        margin-left: 0%;
    }
    .fullWidthfileDescription {
        max-width: 35%;
        width: 35%;
    }
}

.fullWidthfileMime {
    text-transform: uppercase;
    text-align: right;
}

.floatRight {
    float: right;
    margin-left: 8px;
}

.qEditIconsImg {
    height: 16px;
}

.qEditIconsDiv {
    float: right;
    display: none;
    width: 30px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    margin-bottom: -20px;
    text-align: right;
    padding-top: 16px;

    margin-left: 8%;
    max-width: 9%;
    width: 9%;
}

#qEditBtnDone {
    display: none;
}

.dropzone {
    display: none;
    position: fixed;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    width: 100%;
    height: 100%;
    z-index: 1002;
    cursor: pointer;
    background: #EEEEEE;	
}

.dropzone p {
    text-align: center;
    position: fixed;
    text-align: center;
    width: 100%;
    top: 37%;
    font-weight: lighter;
    font-family: "Open Sans",sans-serif;
    font-size: 18px;
    
    animation: popdrop 1s infinite;
    -webkit-animation: popdrop 1s infinite;
}

@keyframes popdrop {
    from{transform:scale(0.9)}
    80%{transform:scale(1.08)}
    to{transform:scale(0.9)}
}
@-webkit-keyframes popdrop {
    from{-webkit-transform:scale(0.8)}
    80%{-webkit-transform:scale(1.02)}
    to{-webkit-transform:scale(1)}
}

#editbar {
    display: none;
    
    position: fixed;
    top:55px;
    margin: auto;
    width: 100%;
    max-width: 680px;
    background-color: #FCFCFC;
    cursor: pointer;
    padding: 10px 10px;
    font-family: sans-serif;
    font-size: 16px;
    color: #F80B6D;
    text-align: left;
    z-index: 999;    
    border: solid 1px #E6E7E6;
    border-top: none;
    background-color: #FFF;
    border-radius: 1px;
    
    -webkit-box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.05);
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.05);
}

@media only all and (max-width: 707px){#editbar{width: 96%;}}@media only all and (max-width: 666px){#editbar{width: 95.9%;}}@media only all and (max-width: 649px){#editbar{width: 95.8%;}}@media only all and (max-width: 634px){#editbar{width: 95.7%;}}@media only all and (max-width: 617px){#editbar{width: 95.5%;}}@media only all and (max-width: 591px){#editbar{width: 95.4%;}}@media only all and (max-width: 580px){#editbar{width: 95%;}}@media only all and (max-width: 552px){#editbar{width: 94.6%;}}@media only all and (max-width: 500px){#editbar{width: 94.2%;}}@media only all and (max-width: 461px){#editbar{width: 93.8%;}}@media only all and (max-width: 430px){#editbar{width: 93.4%;}}@media only all and (max-width: 404px){#editbar{width: 93.0%;}}@media only all and (max-width: 381px){#editbar{width: 92.6%;}}@media only all and (max-width: 360px){#editbar{width: 91.6%;}}@media only all and (max-width: 325px){#editbar{width: 91.0%;}}@media only all and (max-width: 310px){#editbar{width: 90.6%;}}@media only all and (max-width: 287px){#editbar{width: 90.2%;}}@media only all and (max-width: 274px){#editbar{width: 89.6%;}}@media only all and (max-width: 274px){#editbar{width: 89%;}}

.editbarDiv {
    display: inline-block;
    float: left;
    margin-right: 18px;
    
    max-width: 15%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    
    transition: all .16s ease-in-out;
    -moz-transition: all .16s ease-in-out;
		-webkit-transition: all .16s ease-in-out;
}

.editbarDiv:hover {
    transform: scale(1.1);
    -moz-transform: scale(1.1);
		-webkit-transform: scale(1.1);
}

.editbarIcon {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%); 
    
		transition: all .16s ease-in-out;
    -moz-transition: all .16s ease-in-out;
		-webkit-transition: all .16s ease-in-out;
}

.editbarIconRight {
    width: 15px;
    float: right; 
    
    transition: all .16s ease-in-out;
    -moz-transition: all .16s ease-in-out;
		-webkit-transition: all .16s ease-in-out;
}

.editbarIconRight:hover {
    transform: scale(1.1);
    -moz-transform: scale(1.1);
		-webkit-transform: scale(1.1);
}

.editbarIconLeft {
    width: 15px;
    float: left;
    margin-right: 5px;
}

.editbarText {
    font-weight: lighter;
    font-family: "Open Sans",sans-serif;
    font-size: 14px;
    display: inline-block;
    margin: 0;
    color: #000000;
}

.lightbox {
    position: fixed;
    width: 100%;
    max-width: 695px;
    max-height: 70%;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #FFFFFF;
    display: none;
    z-index: 1001;
    border-radius: 0px;
    padding: 0px 8px;
    text-align: center;
    top: 13%;
    
    -webkit-box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.2), 0px 1px 3px 0px rgba(0, 0, 0, 0.05), 0px 1px 0px rgba(255, 255, 255, 0.25) inset;
    -moz-box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.2), 0px 1px 3px 0px rgba(0, 0, 0, 0.05), 0px 1px 0px rgba(255, 255, 255, 0.25) inset;
    box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.2), 0px 1px 3px 0px rgba(0, 0, 0, 0.05), 0px 1px 0px rgba(255, 255, 255, 0.25) inset;
}

@media only all and (max-width: 707px){.lightbox{width: 96%;}}@media only all and (max-width: 666px){.lightbox{width: 95.9%;}}@media only all and (max-width: 649px){.lightbox{width: 95.8%;}}@media only all and (max-width: 634px){.lightbox{width: 95.7%;}}@media only all and (max-width: 617px){.lightbox{width: 95.5%;}}@media only all and (max-width: 591px){.lightbox{width: 95.4%;}}@media only all and (max-width: 580px){.lightbox{width: 95%;}}@media only all and (max-width: 552px){.lightbox{width: 94.6%;}}@media only all and (max-width: 500px){.lightbox{width: 94.2%;}}@media only all and (max-width: 461px){.lightbox{width: 93.8%;}}@media only all and (max-width: 430px){.lightbox{width: 93.4%;}}@media only all and (max-width: 404px){.lightbox{width: 93.0%;}}@media only all and (max-width: 381px){.lightbox{width: 92.6%;}}@media only all and (max-width: 360px){.lightbox{width: 91.6%;}}@media only all and (max-width: 325px){.lightbox{width: 91.0%;}}@media only all and (max-width: 310px){.lightbox{width: 90.6%;}}@media only all and (max-width: 287px){.lightbox{width: 90.2%;}}@media only all and (max-width: 274px){.lightbox{width: 89.6%;}}@media only all and (max-width: 274px){.lightbox{width: 89%;}}

.noscript {
    position: fixed;
    width: 100%;
    height: 100%;
    left: 0px;
    right: 0px;
    top: 0px;
    right: 0px;
    padding-top: 8%;
    background-color: #F2F2F2;
    z-index: 9000;
}

.noscript a {
    color: #55ACEE;
}

.noscriptContainer {
    max-width: 638px;
    width: 80%;
    margin: auto;
}

/* CSS Animations */
.popout {
    animation: popout .5s ease;
    -webkit-animation: popout .5s ease;
}
@keyframes popout {
    from{transform:scale(0.8)}
    80%{transform:scale(1.02)}
    to{transform:scale(1)}
}
@-webkit-keyframes popout {
    from{-webkit-transform:scale(0.8)}
    80%{-webkit-transform:scale(1.02)}
    to{-webkit-transform:scale(1)}
}

.deleteAnimationBlock {
    animation: deleteAnimation .5s ease;
    -webkit-animation: deleteAnimation .5s ease;
}
@keyframes deleteAnimationBlock {
    from {
        transform: scale(1)
    }
    to {
        transform: scale(0);
        width: 0px;
        display: none;
    }
}
@-webkit-keyframes deleteAnimationBlock {
    from {
        -webkit-transform: scale(1)
    }
    to {
        -webkit-transform: scale(0);
        width: 0px;
        display: none;
    }
}