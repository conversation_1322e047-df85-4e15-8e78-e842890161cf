@props([
    'index',
    'key',
    'column',
    'db_fields',
    'is_new' => false
])

<div class="column-item border rounded-lg overflow-hidden cursor-move"
     x-data="{ itemOpen: {{ $is_new ? 'true' : 'false' }} }">
    <div class="flex items-center bg-gray-50 p-2">
        <div class="drag-handle mr-2 text-gray-400 hover:text-gray-600">
            ⋮⋮
        </div>
        <x-forms-button type="button"
                @click="itemOpen = !itemOpen"
                variant="secondary"
                icon="chevron-down"
                :::class="{'rotate-180': itemOpen}"
        />
        <x-forms-input
                type="text"
                :name="'columns['. $index . '][field_name]'"
                :value="$key"
        />
    </div>
    <div x-show="itemOpen" x-transition class="p-3 border-t bg-white">
        <div class="space-y-3">
            <x-forms-input
                    type="text"
                    label="Label"
                    :name="'columns['. $index . '][label]'"
                    :value="$column['label']"
            />
            @php
                print_rr($db_fields,"ccaddd_db_fields",true,true);
            @endphp
            <div>
                <label for="fields_{{ $index }}" class="block text-sm/6 font-medium text-gray-900">Fields</label>
                <div class="mt-2" x-data="{
                    selectedOption: '',
                    fieldValues: {{ is_array($column['field']) ? json_encode($column['field']) : json_encode(explode(', ', $column['field'])) }},
                    addField() {
                        if (this.selectedOption && !this.fieldValues.includes(this.selectedOption)) {
                            this.fieldValues.push(this.selectedOption);
                            this.selectedOption = '';
                            this.updateHiddenField();
                        }
                    },
                    removeField(field) {
                        this.fieldValues = this.fieldValues.filter(f => f !== field);
                        this.updateHiddenField();
                    },
                    updateHiddenField() {
                        this.$refs.hiddenInput.value = this.fieldValues.join(', ');
                    }
                }">
                    <input type="hidden"
                           name="columns[{{ $index }}][field]"
                           x-ref="hiddenInput"
                           :value="fieldValues.join(', ')">
                    <div class="flex items-center space-x-2 mb-2">
                        <div class="flex-grow">
                            <x-forms-select-invisible
                                id="field_select_{{ $index }}"
                                x-model="selectedOption"
                                label="Select Field"
                                class="w-full appearance-none rounded-md py-1.5 pl-3 pr-7 text-base text-gray-500 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                                :options="array_combine($db_fields,$db_fields)"
                            />
                        </div>
                        <button type="button"
                                @click="addField()"
                                :disabled="!selectedOption"
                                class="flex shrink-0 items-center gap-x-1.5 bg-white px-3 py-2 text-sm font-semibold text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 hover:bg-gray-50 focus:relative focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 disabled:opacity-50 disabled:cursor-not-allowed">
                            Add
                        </button>
                    </div>
                    <div class="flex flex-wrap gap-2 p-2 border border-gray-300 rounded-md min-h-10" id="fields_container_{{ $index }}" x-init="new Sortable($el, { animation: 150 })">
                        <template x-for="field in fieldValues" :key="field">
                            <span class="inline-flex items-center gap-x-0.5 rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-gray-500/10 ring-inset">
                                <span x-text="field"></span>
                                <button type="button" @click="removeField(field)" class="group relative -mr-1 size-3.5 rounded-xs hover:bg-gray-500/20">
                                    <span class="sr-only">Remove</span>
                                    <svg viewBox="0 0 14 14" class="size-3.5 stroke-gray-600/50 group-hover:stroke-gray-600/75">
                                        <path d="M4 4l6 6m0-6l-6 6" />
                                    </svg>
                                    <span class="absolute -inset-1"></span>
                                </button>
                            </span>
                        </template>
                    </div>
                </div>
            </div>

            <div class="col-span-2">
                <label class="inline-flex items-center">
                    <input type="checkbox" name="columns[{{ $index }}][auto_filter]"
                           {{ isset($column['auto_filter']) && $column['auto_filter'] ? 'checked' : '' }}
                           class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    <span class="ml-2 text-sm text-gray-600">Enable Auto Filter</span>
                </label>
            </div>

            <!-- Filter Values Section -->
            <div x-data="{ showFilters: {{ isset($column['filters']) && !empty($column['filters']) ? 'true' : 'false' }} }" class="mt-3">
                <div class="flex items-center">
                    <button @click="showFilters = !showFilters" type="button"
                            class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50">
                        <span x-text="showFilters ? 'Hide Filters' : 'Add Filters'"></span>
                    </button>
                </div>

                <div x-show="showFilters" class="mt-2 space-y-2">
                    <div id="filters_container_{{ $index }}" class="space-y-2">
                        @if(isset($column['filters']) && is_array($column['filters']))
                            @foreach($column['filters'] as $filterKey => $filterValue)
                                <x-subscription-table-config-filter-row
                                    :columnIndex="$index"
                                    :filterKey="$filterKey"
                                    :filterValue="$filterValue"
                                />
                            @endforeach
                        @endif
                    </div>
                    <button type="button"
                            class="inline-flex items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                            hx-post="api/add_filter_row"
                            hx-target="#filters_container_{{ $index }}"
                            hx-swap="beforeend"
                            hx-vals='{"columnIndex": "{{ $index }}"}'>
                        Add Filter Value
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>