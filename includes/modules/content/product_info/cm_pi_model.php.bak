<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2016 osCommerce

  Released under the GNU General Public License
*/

  class cm_pi_model {
    var $code;
    var $group;
    var $title;
    var $description;
    var $sort_order;
    var $enabled = false;

    function __construct() {
      $this->code = get_class($this);
      $this->group = basename(dirname(__FILE__));

      $this->title = MODULE_CONTENT_PI_MODEL_TITLE;
      $this->description = MODULE_CONTENT_PI_MODEL_DESCRIPTION;
      $this->description .= '<div class="secWarning">' . MODULE_CONTENT_BOOTSTRAP_ROW_DESCRIPTION . '</div>';

      if ( defined('MODULE_CONTENT_PI_MODEL_STATUS') ) {
        $this->sort_order = MODULE_CONTENT_PI_MODEL_SORT_ORDER;
        $this->enabled = (MODULE_CONTENT_PI_MODEL_STATUS == 'True');
      }
    }
    public static function build_model_ui($product_info,$products_attributes,$selected_attributes){
        if ($product_info['manufacturers_id'] > 0) {
            $manufacturer_query = tep_db_query("select manufacturers_name from manufacturers where manufacturers_id = '" . (int)$product_info['manufacturers_id'] . " LIMIT 1'");
            if ( $manufacturer = tep_db_fetch_array($manufacturer_query)) {
                 $products_manufacturer = $manufacturer['manufacturers_name'];
            }
        }
        $content_width = (int)MODULE_CONTENT_PI_MODEL_CONTENT_WIDTH;
        $products_model = $product_info['products_model'] ?? '';
        $products_gtin = $product_info['products_gtin'] ?? '';

        if ($products_attributes->has_variations){
            $variations = $products_attributes->get_current_selected_variation();
            //print_rr($variations);
            //echo 'mpn: ' . $variations['mpn'];
            $products_model = $variations['model'] ?? $products_model;
            $products_gtin = $variations['gtin'] ?? $products_gtin;
        }
        
        /*$products_variations_query = "SELECT * FROM products_variations WHERE products_id = '" . $product_info['products_id'] . "'";
            $sql = tep_db_query($products_variations_query);   */

        /*foreach ($variations as $variation) {
            $attribs = substr($_GET['products_id'],strpos($_GET['products_id'],'{'));
            if ( $products_attributes->compare_attributes($attribs,$variation['attributes'])){
                $products_model = $variations['model'];
                break;
            }
        }*/
        //if (@tep_not_null($products_model)) {
        ob_start();
        include('includes/modules/content/product_info/templates/tpl_cm_pi_model.php');
        return ob_get_clean();
    }
    function execute() {
      global $oscTemplate, $product_info, $products_attributes;      


        $template = self::build_model_ui($product_info,$products_attributes,$products_attributes->selected_attributes);
		$oscTemplate->addContent($template, $this->group);

    }

    function isEnabled() {
      return $this->enabled;
    }

    function check() {
      return defined('MODULE_CONTENT_PI_MODEL_STATUS');
    }

    function install() {
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Enable Model Module', 'MODULE_CONTENT_PI_MODEL_STATUS', 'True', 'Should this module be shown on the product info page?', '6', '1', 'tep_cfg_select_option(array(\'True\', \'False\'), ', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, set_function, date_added) values ('Content Width', 'MODULE_CONTENT_PI_MODEL_CONTENT_WIDTH', '12', 'What width container should the content be shown in?', '6', '1', 'tep_cfg_select_option(array(\'12\', \'11\', \'10\', \'9\', \'8\', \'7\', \'6\', \'5\', \'4\', \'3\', \'2\', \'1\'), ', now())");
      tep_db_query("insert into configuration (configuration_title, configuration_key, configuration_value, configuration_description, configuration_group_id, sort_order, date_added) values ('Sort Order', 'MODULE_CONTENT_PI_MODEL_SORT_ORDER', '75', 'Sort order of display. Lowest is displayed first.', '6', '0', now())");
    }

    function remove() {
      tep_db_query("delete from configuration where configuration_key in ('" . implode("', '", $this->keys()) . "')");
    }

    function keys() {
      return array('MODULE_CONTENT_PI_MODEL_STATUS', 'MODULE_CONTENT_PI_MODEL_CONTENT_WIDTH', 'MODULE_CONTENT_PI_MODEL_SORT_ORDER');
    }
  }
  