[database_errors] [2025-06-08 21:52:32] [database.class.php:393] 
<!--
********************************************************************************************************************************************************
functions.php > tcs_log() 115
array(13) {
  ["error_code"]: string(5) "HY093"
  ["error_message"]: string(41) "SQLSTATE[HY093]: Invalid parameter number"
  ["sql_state"]: string(5) "HY093"
  ["driver_error_code"]: string(7) "Unknown"
  ["driver_error_message"]: string(7) "Unknown"
  ["query"]: string(100) "SELECT id, name, route_key FROM autobooks_navigation WHERE parent_path = ? AND route_key = ? LIMIT 1"
  ["parameters"]: array(2) {
    [":param0"]: string(0) ""
    [":param1"]: string(7) "sketch9"
  }
  ["table"]: string(20) "autobooks_navigation"
  ["user_id"]: int(2)
  ["request_uri"]: string(94) "/baffletrain/autocadlt/autobooks/api/system/nav_tree/delete_nav_entry?parent_path=&key=sketch9"
  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"
  ["ip_address"]: string(12) "************"
  ["stack_trace"]: string(1216) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(361): PDOStatement->execute()
#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(201): system\database->executeQuery()
#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(208): system\database->get()
#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(514): system\database->first()
#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(53): api\nav_tree\delete_nav_entry()
#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(171): include('...')
#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(164): edge\edge::phprender()
#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()
#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(22): system\router::route()
#9 {main}"
}

    ---------------------------------------------------------------------------- 
      Function: tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 393
        Arguments: 
         0: {"error_code":"HY093","error_message":"SQLSTATE[HY093]: Invalid parameter number","sql_state":"HY093...
         1: "database_errors"
         2: true
      Function: handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 365
        Arguments: 
         0: {"errorInfo":["HY093"]}
         1: "SELECT id, name, route_key FROM autobooks_navigation WHERE parent_path = ? AND route_key = ? LIMIT ...
         2: {":param0":"",":param1":"sketch9"}
      Function: executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 201
        Arguments: 
         0: "SELECT id, name, route_key FROM autobooks_navigation WHERE parent_path = ? AND route_key = ? LIMIT ...
         1: {":param0":"",":param1":"sketch9"}
      Function: get, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 208
        Arguments: 
      Function: first, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php, Line: 514
        Arguments: 
      Function: api\nav_tree\delete_nav_entry, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php, Line: 53
        Arguments: 
         0: {"parent_path":"","key":"sketch9"}
      Function: include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
        Arguments: 
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php"
      Function: phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
        Arguments: 
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php"
      Function: render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 208
        Arguments: 
         0: "layout-api"
         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
      Function: route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 22
        Arguments: 

----------------------------------------------------------------------------
-->

[database_errors] [2025-06-08 22:01:06] [database.class.php:393] 
<!--
********************************************************************************************************************************************************
functions.php > tcs_log() 115
array(13) {
  ["error_code"]: string(5) "HY093"
  ["error_message"]: string(41) "SQLSTATE[HY093]: Invalid parameter number"
  ["sql_state"]: string(5) "HY093"
  ["driver_error_code"]: string(7) "Unknown"
  ["driver_error_message"]: string(7) "Unknown"
  ["query"]: string(100) "SELECT id, name, route_key FROM autobooks_navigation WHERE parent_path = ? AND route_key = ? LIMIT 1"
  ["parameters"]: array(2) {
    [":param0"]: string(4) "root"
    [":param1"]: string(7) "sketch9"
  }
  ["table"]: string(20) "autobooks_navigation"
  ["user_id"]: int(2)
  ["request_uri"]: string(98) "/baffletrain/autocadlt/autobooks/api/system/nav_tree/delete_nav_entry?parent_path=root&key=sketch9"
  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"
  ["ip_address"]: string(12) "************"
  ["stack_trace"]: string(1216) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(361): PDOStatement->execute()
#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(201): system\database->executeQuery()
#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(208): system\database->get()
#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(514): system\database->first()
#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(53): api\nav_tree\delete_nav_entry()
#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(171): include('...')
#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(164): edge\edge::phprender()
#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()
#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(22): system\router::route()
#9 {main}"
}

    ---------------------------------------------------------------------------- 
      Function: tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 393
        Arguments: 
         0: {"error_code":"HY093","error_message":"SQLSTATE[HY093]: Invalid parameter number","sql_state":"HY093...
         1: "database_errors"
         2: true
      Function: handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 365
        Arguments: 
         0: {"errorInfo":["HY093"]}
         1: "SELECT id, name, route_key FROM autobooks_navigation WHERE parent_path = ? AND route_key = ? LIMIT ...
         2: {":param0":"root",":param1":"sketch9"}
      Function: executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 201
        Arguments: 
         0: "SELECT id, name, route_key FROM autobooks_navigation WHERE parent_path = ? AND route_key = ? LIMIT ...
         1: {":param0":"root",":param1":"sketch9"}
      Function: get, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 208
        Arguments: 
      Function: first, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php, Line: 514
        Arguments: 
      Function: api\nav_tree\delete_nav_entry, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php, Line: 53
        Arguments: 
         0: {"parent_path":"root","key":"sketch9"}
      Function: include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
        Arguments: 
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php"
      Function: phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
        Arguments: 
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php"
      Function: render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 208
        Arguments: 
         0: "layout-api"
         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
      Function: route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 22
        Arguments: 

----------------------------------------------------------------------------
-->

