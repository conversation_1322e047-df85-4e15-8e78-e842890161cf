<?php

require_once "includes/application_top.php";
require_once "includes/classes/autodesk_wsapi_v2.php";
require_once "includes/functions/tcs_functions.php";
require_once "vendor/autoload.php";

//connect to test database


/*{
"id":"512936b1-a31b-4870-9412-0b76d9a6fcdd"
"topic":"quote-status"
"event":"changed"
"sender":"PWS Quote Status"
"publishedAt":"2024-08-08T13:44:49.355Z"
"csn":"5103159758"
"environment":"stg"
"payload":{
"quoteNumber":"Q-00859"
"quoteStatus":"Ordered"
"transactionId":"13847669-f969-4e2a-bfba-a9ac3c051a21"
"message":"Quote# Q-01522 status changed to Ordered."
"modifiedAt":"2024-08-08T13:44:49.355Z"
}
}*/


// Get headers
$headers = [];
foreach ($_SERVER as $name => $value) {
    if (substr($name, 0, 5) == 'HTTP_') {
        $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
    }
}

// Get the raw POST data
$raw_post_data = file_get_contents('php://input');

// Decode the JSON payload
$json_payload = json_decode($raw_post_data, true);

switch ($json_payload['topic']) {
    case 'quote-status':
        switch ($json_payload['event']) {
            case 'created':                
                break;
            case 'changed':
                break;
            case 'deleted':
                break;
        }
        log_message(subject:'quote_update',message:"Quote Status changed for quote number: " . $json_payload['payload']['quoteNumber'] . " - " . $json_payload['payload']['message']);
        $params = [
            ':quoteNumber' => $json_payload['payload']['quoteNumber'],
            ':quoteStatus' => $json_payload['payload']['quoteStatus'],
            ':transactionId'=> $json_payload['payload']['transactionId'],
            ':message' => $json_payload['payload']['message']
        ];
        $where_sql = [];
        $quoteNumber = $json_payload['payload']['quoteNumber'];
        $transactionId = $json_payload['payload']['transactionId'];
        log_message(subject:'quote_update',message:"looking for quote number: " . $quoteNumber . " or " . $transactionId);
        if (isset($transactionId) OR isset($quoteNumber)) {    
            log_message(subject:'quote_update',message:"Found quote number: " . $quoteNumber . " or " . $transactionId);
            if ($quoteNumber != null && $quoteNumber != '' && $quoteNumber != 'NA') {
                $where_sql[] = "`quoteNumber` = :w_quoteNumber";
                $params[':w_quoteNumber'] = $quoteNumber;
            }
            if ($transactionId != null && $transactionId != '' && $transactionId != 'NA') {
                $where_sql[] = "`transactionId` = :w_transactionId";
                $params[':w_transactionId'] = $transactionId;
            } 
            $wsp = ['\n', '\r', '\t', '  '];
            log_message(subject:'quote_update', message:'built where array:'  . preg_replace('/\s+/', ' ', trim(print_r($where_sql,true))));
            log_message(subject:'quote_update', message:'built params array:'  . preg_replace('/\s+/', ' ', trim(print_r($params,true))));
                 
            $query_sql = "UPDATE autodesk_quotes SET `quoteStatus` = :quoteStatus, `transactionId` = :transactionId,`quoteNumber` = :quoteNumber, `message` = :message, `modifiedAt` = NOW() WHERE " . implode(' OR ', $where_sql);
            if (count($where_sql) > 0){
                log_message(subject:'quote_update',message:'performing query:'. preg_replace('/\s+/', ' ', trim(print_r($query_sql,true))));                
                $result = tep_db_query($query_sql,null,$params,true);
                log_message(subject:'quote_update',message:'result:'. preg_replace('/\s+/', ' ', trim(print_r($result,true))));
            } else {
                log_message(subject:'quote_update',message:"No quote number or transaction id found");
            }            
            $response = ['success' => 1];
        } else {
            log_message(subject:'quote_update',message:"No quote number or transaction id found");      
            $response = ['success' => 0];
        }
        log_message(subject:'quote_update',message:"complete, success: " . $response['success']);      
        break;
    case 'product-catalog':
        switch ($json_payload['event']) {            
            case 'changed':
               /* $autodesk = new AutodeskAPI(); 
                $response = $autodesk->get_autodesk_product_catalog();*/
                break;
        
        }
        case 'subscription-change':
            // Define log file path
            $log_file = 'logs/subscription_change.log';
            $mapped_keys = [];
            // Log the start of the process
            error_log('Starting subscription change processing at ' . date('Y-m-d H:i:s'), 3, $log_file);
            
            try {
                $mapping = AutodeskSubscriptions::get_subscription_column_mapping();
                
                // Log the mapping for debugging
                error_log('Subscription column mapping retreived', 3, $log_file);
                
                $query_sql = '';
                $query_array = [];
                
                // Log the incoming payload
                error_log('Incoming payload: ' . print_r($json_payload['payload'], true), 3, $log_file);
                $autodesk = new AutodeskAPI;
                $subscription = $autodesk->subscription->get_from_api($json_payload['payload']['subscriptionId']);
                error_log("Got subscription: " . print_r($subscription,true), 3, $log_file);
                // Process payload
                foreach ($subscription[0] as $key => $value) {
                    $found  = false;
                    foreach ($mapping as $table_Name => $table){
                        if (!empty($table['columns'][$key])) {
                            $found = true;
                            $mapping_current = [$table_Name,$key];
                            
                            if (!empty($mapping_current[0]) && !empty($mapping_current[1])) {
                                $query_array['tables'][$mapping_current[0]]['fields'][$mapping_current[1]] = var_export($value, true);
                                // Log successful mapping
                                $mapped_keys[$mapping_current[0]] = $mapping_current[1];
                            }
                            break;
                        }
                    }

                    if (!$found){ 
                            // Log skipped keys 
                            $skipped_keys[] = error_log("Skipped unmapped key: $key, ", 3, $log_file);
                          // error_log("Skipped unmapped key: $key, ", 3, $log_file);
                    }  
                }


                foreach ($query_array['tables'] as $table => $fields) {
                    $set_fields = [];
                    foreach ($fields['fields'] as $key => $value) {
                        $set_fields[] = "{$key} = {$value}";
                    }
                    
                    $set_string = implode(', ', $set_fields);
                    $query_sql .= "INSERT INTO {$table} SET {$set_string} " . 
                                  "ON DUPLICATE KEY UPDATE {$set_string};" . PHP_EOL;


                    // Log generated SQL query
                    tep_db_query($query_sql);
                }
               
                $response = [
                    'struct' => $query_array,
                    'query_sql' => $query_sql
                ];
                
                // Log successful completion
                error_log('Subscription change processing completed successfully' . PHP_EOL . 'mapped:' . print_r($mapped_keys,true) . PHP_EOL . 'Skipped:' . print_r($skipped_keys,true), 3, $log_file);
                
            } catch (Exception $e) {
                // Log any exceptions that occur
                error_log('Error in subscription change processing: ' . $e->getMessage(), 3, $log_file);
                error_log(PHP_EOL . 'mapped:' . print_r($mapped_keys,true) . PHP_EOL . 'Skipped:' . print_r($skipped_keys,true), 3, $log_file);
              
                error_log('Trace: ' . $e->getTraceAsString(), 3, $log_file);
                
                // Optionally set an error response
                $response = [
                    'error' => true,
                    'message' => $e->getMessage()
                ];
            }
            break;
    default:
        $response = "['status' => 'Nothing to do]";
        break;
}
header('Content-type: application/json');
echo json_encode($response);
?>