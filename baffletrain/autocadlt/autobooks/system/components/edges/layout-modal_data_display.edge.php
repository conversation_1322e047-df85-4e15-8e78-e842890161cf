@props([
    'title' => 'Data Display',
    'description' => '',
    'items' => [], 
    'layout' => [],
    'content' => [],
    'class' => ''
])
<div class="grid grid-cols-3 bg-gray-200">
@foreach ($layout as $card)
    <div class="flex flex-col {{ $card['class_suffix'] ?? '' }}">
    <x-{{ $card['type'] ?? 'layout-card' }}
        :label="$card['label'] ?? ''"
        collapsible="true" 
        collapsed="{{ $card['collapsed'] ?? 'false' }}"
        class_suffix=""
        internal_class_suffix="{{ $card['internal_class_suffix'] ?? '' }}"
    >

        @foreach ($card['content'] as $key => $column)
            @if(!isset($column['type'])) {{ $column['type'] = 'layout-card' }} @endif
            @if ($column['type'] == 'function' && is_callable($column['content']))
               {{ $column['content']() }}
            @elseif (str_starts_with($column['type'],'html-' && $html_el = str_replace('html-','',$column['type'])))
                <{{ $html_el }}>{{ $column['content'] }}</{{ $html_el }}>
            @else
                @if (!isset($column['content']))
                    <div class="px-1 py-1 h-min sm:grid sm:grid-cols-2 sm:gap-4 sm:px-0">
                        <dt class="text-sm/6 font-medium text-gray-900">{{ $column['label'] }}</dt>
                        <dd class="mt-1 text-sm/6 text-gray-700 sm:mt-0">
                            {{ $column['value'] }}
                        </dd>
                    </div>
                @else
                    @if (!isset($column['type']) || $column['type'] == 'layout-card' || $column['type'] == 'layout-box')
                        <x-{{ $column['type'] }}
                            :label="$column['label']"
                            collapsible="true"
                            :collapsed="{{ $column['collapsed'] ?? 'false' }}"
                            :class_suffix="{{ $column['class_suffix'] }}"
                            :internal_class_suffix="{{ $column['internal_class_suffix'] }}"
                        >
                            @foreach ($column['content'] as $subKey => $subColumn)
                                <div class="{{ isset($column['hide_labels']) && $column['hide_labels'] ? 'px-0 py-0' : 'px-1 py-1' }}  h-min sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                                    <dt class="text-sm/6 font-medium text-gray-900 {{ isset($column['hide_labels']) && $column['hide_labels'] ? 'hidden' : '' }}">
                                        {{ $subColumn['label'] }}
                                    </dt>
                                    <dd class=" text-sm/6 text-gray-700 {{ isset($column['hide_labels']) && $column['hide_labels'] ? 'mt-0 col-span-3' : 'mt-1 sm:col-span-2' }} sm:mt-0">
                                        {{ $subColumn['value'] }}
                                    </dd>
                                </div>
                            @endforeach
                        </x-{{ $column['type'] }}>
                    @elseif (is_string($column['content']))
                        <x-{{ $column['type'] }} x-edge-data='$content[$column["content"]]'>
                    @endif
                @endif
            @endif
        @endforeach
    </x-{{ $card['type'] ?? 'layout-card' }}>
    </div>
@endforeach
</div>