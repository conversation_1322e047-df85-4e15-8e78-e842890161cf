<?php
  /**
  * KISS Image Thumbnailer
  * Creates image thumbnails where the image size requested differs from the actual image size.
  * Ensures that the browser does not have to resize images resulting in far greater loading speeds.
  * Once thumbnails have been created the system has been designed to use very minimal resources.
  *  
  * @license    commercial license https://www.oscaddons.com/license.php/en
  * @copyright  Copyright Rainer <PERSON>ied
  * <AUTHOR> @raiwa <EMAIL>
  * @version    Pro 1.0 BS

  * @package KISS Image Thumbnailer BS
  * @link http://www.fwrmedia.co.uk
  * @copyright Copyright 2008-2009 FWR Media ( <PERSON> )
  * <AUTHOR> FWR Media, http://www.fwrmedia.co.uk 
  * @lastdev $Author:: @raiwa  <EMAIL>       $:  Author of last commit
  * @lastmod $Date:: 2018-02-122       			     						 $:  Date of last commit
  * @version $Rev:: Pro 1.0 BS                               $:  Revision of last commit
  * @Id $Id:: Image_Helper.php 28 BS 2018-02-12 @raiwa       $:  Full Details
  */
  require_once 'includes/modules/kiss_image_thumbnailer/classes/Image.php';

  /**
  * Helper class to create valid thumbnails on the fly within the tep_image() wrapper
  *  
  * 
  * @license    http://www.gnu.org/licenses/gpl-2.0.html GNU Public License)
  * <AUTHOR> fisher - FWR Media ( www.fwrmedia.co.uk )
  * @version     1.0
  */
  class Image_Helper extends ArrayObject {
    /**
    * put your comment there...
    * 
    * @var mixed
    */
    protected $_valid_mime = array( 'image/png', 'image/jpeg', 'image/jpg', 'image/gif' );
    /**
    * put your comment there...
    * 
    */
    public function __construct( $input ) {
      parent::__construct( $input, parent::ARRAY_AS_PROPS );  
    } // end constructor
    /**
    * put your comment there...
    * 
    */
    public function assemble() {
      $image_check = $this->_checkImage();
      // Image check is bad so we pass control back to the old OsC image wrapper
      if ( 'abort' == $image_check ) {
        return false;
      }
      // If we have to only we generate a thumb .. this is very resource intensive
      if ( 'no_thumb_required' !== $image_check) {
        $this->_generateThumbnail();
      }
      if ( KISSIT_USE_RETINA_IMAGES == 'true' && strpos($this->parameters, 'no-subset') === false && $this->attributes['alt'] != '_kissit_image' && $this->attributes['width']*KISSIT_RETINA_FACTOR < getimagesize($this->_original_image_src)[0] && $this->attributes['height']*KISSIT_RETINA_FACTOR < getimagesize($this->_original_image_src)[1] ) {
        tep_image($this->_original_image_src, '_kissit_image', (int)($this->_calculated_width*2), (int)($this->_calculated_height*2), @tep_not_null( $this->parameters ) ? tep_output_string( $this->parameters ) : '');
      }
      if ( KISSIT_USE_SUBSET == 'true' && strpos($this->parameters, 'no-subset') === false && $this->attributes['alt'] != '_kissit_image' && getimagesize($this->_original_image_src)[0] > 700 && $this->attributes['width'] > 700 ) {
        tep_image($this->_original_image_src, '_kissit_image', (int)($this->_calculated_width/2), (int)($this->_calculated_height/2), @tep_not_null( $this->parameters ) ? tep_output_string( $this->parameters ) : '');
      }
      if ( KISSIT_USE_SUBSET == 'true' && strpos($this->parameters, 'no-subset') === false && $this->attributes['alt'] != '_kissit_image' && getimagesize($this->_original_image_src)[0] > 400 && $this->attributes['width'] > 400 ) {
        tep_image($this->_original_image_src, '_kissit_image', (int)($this->_calculated_width/4), (int)($this->_calculated_height/4), @tep_not_null( $this->parameters ) ? tep_output_string( $this->parameters ) : '');
      }
      $this->_build();
      return (string)$this;
    } // end method
    /**
    * put your comment there...
    *  // end method
    * @param mixed $attribs
    */
    protected function _checkImage() {
      if ( !is_file ( $this->src ) ) {
        $this->src = $this->default_missing_image;  
      }
      $image_path_parts = pathinfo ( $this->src );
      $this->_image_name = $image_path_parts['basename'];
      $this->_original_image_info = $this->src;
      $this->_original_image_src = pathinfo ( $this->src )["dirname"] . '/' . pathinfo ( $this->src )["basename"];
      $this->_thumb_filename = str_replace(' ', '-', $this->_image_name);
      $this->_thumb_src = $this->thumbs_dir_path . $this->_thumb_filename;        
      if ( KISSIT_USE_RETINA_IMAGES == 'true' ) {
        $this->_thumb_x2_src = str_replace($this->attributes['width'] . '_' . $this->attributes['height'], ($this->attributes['width']*2) . '_' . ($this->attributes['height']*2), $this->thumbs_dir_path) . $this->_thumb_filename;
      }
      if ( KISSIT_USE_SUBSET == 'true' ) {
        $this->_thumb_half_src = str_replace($this->attributes['width'] . '_' . $this->attributes['height'], (int)($this->attributes['width']/2) . '_' . (int)($this->attributes['height']/2), $this->thumbs_dir_path) . $this->_thumb_filename;
        $this->_thumb_quarter_src = str_replace($this->attributes['width'] . '_' . $this->attributes['height'], (int)($this->attributes['width']/4) . '_' . (int)($this->attributes['height']/4), $this->thumbs_dir_path) . $this->_thumb_filename;
      }
      if ( is_readable ( $this->_thumb_src ) ) {
        $this->_calculated_width = $this->attributes['width'];
        $this->_calculated_height = $this->attributes['height'];
        $this->src = $this->_thumb_src;
        return 'no_thumb_required';
      }
      if ( KISSIT_MAIN_PRODUCT_WATERMARK_SIZE == 0 ) {
      	if ( !$this->_original_image_info = getimagesize ( $this->src ) ) {
      		return 'abort';
      	} 
      	if (!in_array ( $this->_original_image_info['mime'], $this->_valid_mime ) ) {
      		return 'abort';
      	}
      	
      }
    } // end method
    /**
    * put your comment there...
    * 
    */
    protected function _generateThumbnail() {
      if ( $this->attributes['width'] == $this->_original_image_info[0] && $this->attributes['height'] == $this->_original_image_info[1] ) {
        $this->_calculated_width = $this->attributes['width'];
        return $this->_calculated_height = $this->attributes['height'];
      }
      if ( $this->attributes['width'] == 0 || $this->attributes['height'] == 0 ) {
        $this->_calculated_width =  $this->_original_image_info[0];
        return $this->_calculated_height = $this->_original_image_info[1]; 
      }
      //make sure the thumbnail directory exists. 
      if ( !is_writable ( $this->thumbs_dir_path ) ) { 
        trigger_error ( 'Cannot detect a writable thumbs directory!', E_USER_NOTICE );
      }
      if ( is_readable ( $this->_thumb_src ) ) {
        $this->_calculated_width =  (int)$this->attributes['width'];
        $this->_calculated_height = (int)$this->attributes['height'];
        return $this->src = $this->_thumb_src;  
      }
      // resize image
      $image = new Image();
      $image->open( $this->src, $this->thumb_background_rgb )
            ->resize( (int)$this->attributes['width'], (int)$this->attributes['height'], (strpos($this->parameters, 'no-watermark') !== false)? 0 : KISSIT_MAIN_PRODUCT_WATERMARK_SIZE)
            ->save( $this->_thumb_src, (int)$this->thumb_quality );
      $this->_thumbnail = $image;
      $this->_calculated_width = $this->_thumbnail->getWidth();
      $this->_calculated_height = $this->_thumbnail->getHeight();
      $this->src = $this->_thumb_src;
    } // end method
    /**
    * put your comment there...
    *  // end method
    */
    protected function _build() {
      $alt_title = $this->isXhtml ? tep_output_string_protected( str_replace ( '&amp;', '&', $this->attributes['alt'] ) ) : tep_output_string( $this->attributes['alt'] );
      $parameters = @tep_not_null( $this->parameters ) ? tep_output_string( $this->parameters ) : false;
      $width = (int)$this->_calculated_width;
      $height = (int)$this->_calculated_height;
      $this->_html = '<img width="' . $width . '" height="' . $height . '" src="' . $this->src . '"';
      // Build srcset BEGIN
      $srcset_content = null;
      if ( KISSIT_USE_RETINA_IMAGES == 'true' && is_file($this->_thumb_x2_src) ) $srcset_content .= $this->src . ' 1x, ' . $this->_thumb_x2_src . ' 2x';
      if ( KISSIT_USE_SUBSET == 'true' && is_file($this->_thumb_quarter_src)) $srcset_content .= ' ' . $this->_thumb_quarter_src . ' ' . (int)($this->_calculated_width/4) . 'w';
      if ( KISSIT_USE_SUBSET == 'true' && is_file($this->_thumb_half_src)) $srcset_content .= ', ' . $this->_thumb_half_src . ' ' . (int)($this->_calculated_width/2) . 'w';
      if ( KISSIT_USE_SUBSET == 'true' && (is_file($this->_thumb_half_src) || is_file($this->_thumb_quarter_src)) ) $srcset_content .= ', ' . $this->src . ' ' . (int)($this->_calculated_width) . 'w';
      if ( KISSIT_USE_SUBSET == 'true' && (is_file($this->_thumb_half_src) || is_file($this->_thumb_quarter_src)) ) $srcset_content .= '" sizes="';
      if ( KISSIT_USE_SUBSET == 'true' && is_file($this->_thumb_quarter_src)) $srcset_content .= '(max-width: ' . (int)($this->_calculated_width/4) . 'px) ' . (int)(($this->_calculated_width/4)-40) . 'px';
      if ( KISSIT_USE_SUBSET == 'true' && is_file($this->_thumb_half_src)) $srcset_content .= ', (max-width: ' . (int)($this->_calculated_width/2) . 'px) ' . (int)(($this->_calculated_width/2)-40) . 'px';
      if ( KISSIT_USE_SUBSET == 'true' && (is_file($this->_thumb_half_src) || is_file($this->_thumb_quarter_src)) ) $srcset_content .= ', ' . (int)($this->_calculated_width) . 'px';
      if ( KISSIT_USE_RETINA_IMAGES == 'true' && is_file($this->_thumb_x2_src) || KISSIT_USE_SUBSET == 'true' && (is_file($this->_thumb_half_src) || is_file($this->_thumb_quarter_src)) ) $this->_html .= ' srcset="' . $srcset_content . '"';
      // Build srcset END
      $this->_html .= ' title="' . $alt_title . '" alt="' . $alt_title . '"';
      if ( false !== $parameters ) $this->_html .= ' ' .  html_entity_decode(tep_output_string( $parameters ));
      $this->_html .= $this->isXhtml ? ' />' : '>';      
      
    } // end method
    /**
    * put your comment there...
    * 
    */
    public function __tostring() {
      return $this->_html;
    } // end method
  } // end class