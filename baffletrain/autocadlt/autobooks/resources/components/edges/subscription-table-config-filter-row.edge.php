@props([
    'columnIndex',
    'filterKey' => '',
    'filterValue' => '',
    'is_new' => false
])

<div class="filter-item flex gap-2">
    <input type="text" name="columns[{{ $columnIndex }}][filter_keys][]"
           value="{{ $filterKey }}"
           class="block w-1/2 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
           placeholder="Key">
    <input type="text" name="columns[{{ $columnIndex }}][filter_values][]"
           value="{{ $filterValue }}"
           class="block w-1/2 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
           placeholder="Value">
    <button type="button" class="text-red-600 hover:text-red-800"
            hx-delete="api/remove_filter"
            hx-target="closest .filter-item"
            hx-swap="outerHTML">×
    </button>
</div>