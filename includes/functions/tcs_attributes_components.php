<?php
require_once("includes/functions/tcs_components.php");

// Wrapper function to check for dependency issues - uses class method for consistency
// This function is kept for backward compatibility with existing code
function check_attribute_dependencies($products_attributes) {
    return $products_attributes->check_dependency_issues();
}


function tcs_draw_attributes_dependency_warnings($products_attributes):string {
    // Add dependency check warnings if product ID exists
    $dependency_warnings = '';
    $issues = $products_attributes->check_dependency_issues();
    if (!empty($issues)) {
        // Include CSS for dependency issues if not already included
        $dependency_warnings = '<link rel="stylesheet" href="includes/css/dependency-issues.css">';

        // Add JavaScript for modal functionality
        $dependency_warnings .= '<script>
        function openModal(modalId) {
            document.getElementById(modalId).style.display = "flex";
        }
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = "none";
        }
        </script>';

        $dependency_warnings .= '<div class="alert alert-warning">';
        $dependency_warnings .= '<button type="button" class="close" x-on:click="show = false" aria-label="Close">';
        $dependency_warnings .= '<span aria-hidden="true">&times;</span>';
        $dependency_warnings .= '</button>';
        $dependency_warnings .= '<h4>Attribute Dependency Issues:</h4>';
        $dependency_warnings .= '<ul>';

        $modal_counter = 0;
        // Iterate through issues indexed by attribute_id
        foreach ($issues as $attribute_id => $issue_data) {
            // Check if this attribute has multiple issues (array format) or single issue
            if (isset($issue_data['issues'])) {
                $dependency_warnings .= "<li><strong>Attribute {$attribute_id} {$issue_data['name']}:</strong><ul>";
                // Multiple issues for this attribute
                foreach ($issue_data['issues'] as $index => $single_issue) {
                    $modal_id = "issue_modal_" . $modal_counter++;
                    $issue_type = $single_issue['type'] ?? 'unknown';
                    $explanation = get_dependency_issue_explanation($issue_type);

                    $dependency_warnings .= "<li class='issue-type-{$issue_type}'><strong>{$index}: </strong>{$single_issue['message']}";
                    $dependency_warnings .= "<button class='btn btn-info btn-xs dependency-info-btn' ";
                    $dependency_warnings .= "onclick='openModal(\"{$modal_id}\")' ";
                    $dependency_warnings .= "title='Click for detailed explanation and resolution steps'>ℹ️</button>";
                    $dependency_warnings .= "</li>";

                    // Add modal for this issue
                    $dependency_warnings .= tcs_draw_issue_info_modal($modal_id, $explanation);
                }
                $dependency_warnings .= '</ul></li>';
            } else {
                // Single issue for this attribute
                $modal_id = "issue_modal_" . $modal_counter++;
                $issue_type = $issue_data['type'] ?? 'unknown';
                $explanation = get_dependency_issue_explanation($issue_type);

                $dependency_warnings .= "<li class='issue-type-{$issue_type}'><strong>Attribute ID {$attribute_id}:</strong> {$issue_data['message']}";
                $dependency_warnings .= "<button class='btn btn-info btn-xs dependency-info-btn' ";
                $dependency_warnings .= "onclick='openModal(\"{$modal_id}\")' ";
                $dependency_warnings .= "title='Click for detailed explanation and resolution steps'>ℹ️</button>";
                $dependency_warnings .= "</li>";

                // Add modal for this issue
                $dependency_warnings .= tcs_draw_issue_info_modal($modal_id, $explanation);
            }
        }

        $dependency_warnings .= '</ul>';
        $dependency_warnings .= '</div>';
    }
    return $dependency_warnings;
}

// Modify get_attributes_tables to include dependency checks
function get_attributes_tables($products_id = false):string {
   $no_id = '<strong>Save first to generate product id</strong>';
   if (!$products_id) return $no_id;
   $products_attributes =  new tcs_product_attributes($products_id);

    $dependency_warnings = tcs_draw_attributes_dependency_warnings($products_attributes);

   $attributes_table = tcs_draw_attributes_table($products_attributes, $products_id);
   $variations_table = tcs_draw_variations_table($products_attributes, $products_id );

   print_rr($dependency_warnings,'products_attributes object');
   return "
        
        <div class=\"panel panel-default\">
            <div class=\"panel-body\">
                {$attributes_table} 
                <div id='attributes_error_message'>
                    {$dependency_warnings}
                </div>
            </div>
           
        </div>

        <div class=\"panel panel-default\">
            <div class=\"panel-body\">
               {$variations_table}
                <div id='variations_error_message'>
                </div>
            </div>
        </div>
        ";

}

function tcs_draw_attributes_table($products_attributes, $products_id, $body_only = false, $params = []):string {

    // Define columns for the table
    $columns = [
        ['name' => '&nbsp;', 'class' => '', 'params' => ''],
        ['name' => 'Option', 'class' => '', 'params' => ''],
        ['name' => 'Value', 'class' => '', 'params' => ''],
        ['name' => 'Default', 'class' => 'text-center', 'params' => ''],
        ['name' => 'DependsOn', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Sort Order', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Actions', 'class' => 'text-center', 'params' => ['colspan' => '2']],
    ];

    // Get the attributes
    $attributes = $products_attributes->get_attributes(false);
    print_rr($products_attributes,'attributes_tablz');
    $rows = '';
    foreach ($attributes as $option) {
        foreach ($option['values'] as $key => $value) {
            $rows .= tcs_draw_attributes_table_row( $products_id,$value,[],$products_attributes);
        }
    }

    // If body_only is true, return just the rows
    if ($body_only) {
        return $rows;
    }

    $footer = tcs_draw_attributes_form_footer($products_id,null,$products_attributes);

    // Output the table
    return tcs_draw_admin_bootstrap_table(
        'Attributes',
        'attributesTable',
        'table table-striped table-draggable',
        $columns,
        $rows,
        $footer,
        ['body' => ['hx-get' => 'api_h.php', 'hx-include' => '.attributesSOinput', 'hx-target' => '#attributes_error_message','hx-vals' => '{"action": "product_attributes_updateSortOrder", "products_id": "' . $products_id . '"}', 'hx-trigger' => 'deactivate'] ],
        $body_only
    );
}

function tcs_draw_attributes_table_row($products_id,$attribute,$params = [],$products_attributes = null) {
    print_rr($attribute, 'attribute1');

    // Check if attribute is null or not an array
    if (!is_array($attribute) || $attribute === null) {
        print_rr([
            'error' => 'Attribute parameter is null or not an array',
            'attribute_type' => gettype($attribute),
            'attribute_value' => $attribute
        ], 'tcs_draw_attributes_table_row_null_error');
        return '<tr><td colspan="8">Error: Invalid attribute data (null or not array)</td></tr>';
    }

    // Ensure required fields exist, if not return error
    if (empty($attribute['products_options_id']) || empty($attribute['products_options_values_id'])) {
        print_rr([
            'error' => 'Missing required fields for table row generation',
            'products_options_id' => $attribute['products_options_id'] ?? 'MISSING',
            'products_options_values_id' => $attribute['products_options_values_id'] ?? 'MISSING',
            'attribute_keys' => array_keys($attribute)
        ], 'tcs_draw_attributes_table_row_error');
        return '<tr><td colspan="8">Error: Missing required attribute data</td></tr>';
    }

    if (strpos($attribute['dependson_options_values_id'], ',') !== false) {
        $valueIds = explode(',', $attribute['dependson_options_values_id']);
        $dependson_options_values_name = "";
        foreach ($valueIds as $value) {
            if ($dependson_options_values_name != "") {
                $dependson_options_values_name .= ', ';
            }
            $dependson_options_values_name .= tep_values_name($value);
        }
    } else {
        $dependson_options_values_name = tep_values_name($attribute['dependson_options_values_id']);
    }
    $options_name = $attribute['products_options_name'];
    $options_id = $attribute['products_options_id'];
    $values_name = $attribute['products_options_values_name'];
    $values_id = $attribute['products_options_values_id'];
    $dependson_options_name = tep_options_name($attribute['dependson_options_id']);

    if (@tep_not_null($attribute['dependson_options_id']) && @tep_not_null($attribute['dependson_options_values_id'])) {
        $dependsOn_string = $dependson_options_name . ': ' . $dependson_options_values_name;
    } else {
        $dependsOn_string = "None";
    }
    $rowClass = "";
    $rowId = $options_id . '_' . $values_id;
    print_rr($products_attributes->dependency_issues, 'dependency_issues for ' . $attribute['products_attributes_id']);
    if (isset($products_attributes->dependency_issues[$attribute['products_attributes_id']] ) ) {
        $rowClass = "table-danger danger";
    }


    // Add this row's data to the dataset
    $id = "attributesTableRow_{$rowId}";

    $row_content = [
        ['class' => 'portletTD', 'id' => '', 'content' => '<div class="portlet">&nbsp;</div>'],
        ['class' => '', 'id' => "attributesTable_optionsName_{$rowId}", 'content' => $options_name, 'params' => ['data-options_id' => $options_id]],
        ['class' => '', 'id' => "attributesTable_optionsValueName_{$rowId}", 'content' => $values_name, 'params' => ['data-values_id' => $values_id]],
        ['class' => 'text-center', 'id' => "attributesTable_optionsValuePriceDefault_{$rowId}", 'content' => $attribute["attribute_default"] ? 'Yes' : 'No'],
        ['class' => '', 'id' => "attributesTable_DependsOn_{$rowId}", 'content' => $dependsOn_string,
            'params' => [
                'data-dependson_options_id' => $attribute['dependson_options_id'],
                'data-dependson_values_id' => $attribute['dependson_options_values_id']
            ]
        ],[
            'class' => '',
            'id' => '',
            'content' => '<input class="indexSO attributesSOinput" type="text" size="1" maxlength="4" value="' . $attribute['products_attributes_sort_order'] . '" name="attributesTableSO[' . (int)$attribute['products_attributes_id'] . ']">'
        ],[
            'class' => 'attributesOptionsEdit listsOptionsEdit', 'id' => '',
            'hx-get'=>"api_h.php",
            'hx-target'=>"#attributes_form",
            'hx-vals'=> '{"action": "product_attributes_product_edit","products_id": "' . (int)$_GET['pID'] . '", "products_attributes_id": "' . $attribute['products_attributes_id'] . '"}',
            'hx-trigger' => 'click',
            'content' => 'e'
        ],[
            'class' => 'attributesOptionsDelete listsOptionsDelete',
            'id' => '',
            'hx-get'=>"api_h.php",
            'hx-prompt' => "Are you sure you want to delete this attribute?",
            'hx-vals'=> '{"action": "product_attributes_removeAttribute", "products_id": "' . (int)$_GET['pID'] . '", "products_attributes_id": "' . $attribute['products_attributes_id'] . '"}',
            'hx-trigger' => 'click',
            'content' => 'x'
        ],
    ];
    // Add Alpine.js transition directives for smooth animations
    $alpine_params = [
        'x-data' => 'tableRow()',
        'x-show' => 'show',
        'x-transition:enter' => 'table-row-enter-active',
        'x-transition:enter-start' => 'table-row-enter',
        'x-transition:enter-end' => 'table-row-enter-to',
        'x-transition:leave' => 'table-row-leave-active',
        'x-transition:leave-start' => 'table-row-leave',
        'x-transition:leave-end' => 'table-row-leave-to'
    ];

    $params = array_merge($params, $alpine_params, ['data-attributesid' => $attribute['products_attributes_id'], 'data-rowNum' => $rowId,'hx-target' => 'this']);

    // Output the table row
    return tcs_draw_admin_bootstrap_table_row($id, $rowClass, $row_content, $params, $row_name);
}

function tcs_draw_attributes_form_footer($products_id, $products_attributes_id = false, $products_attributes = false) {
    global $languages_id;
    $attribute = false;
    if ($products_attributes_id) $attribute = tcs_product_attributes::get_attributes_from_id($products_attributes_id);
    $options = tcs_db_query("select * from products_options where language_id = '" . $languages_id . "' order by products_options_name");
    print_rr(  $options ,'optionsz');
    print_rr(  $attribute ,'attributez');
    ob_start();?>
    <div id="attributes_form" x-data="formElement()" x-show="show" x-transition:enter="form-enter-active" x-transition:enter-start="form-enter" x-transition:enter-end="form-enter-to" x-transition:leave="form-leave-active" x-transition:leave-start="form-leave" x-transition:leave-end="form-leave-to">
        <div class="row">
            <div id="attributes_left_container" class="col-xs-6">
                <div id="attributes_options_container" class="form-inline form-group">
                    <label>Attributes: </label><br/>
                    <select id="attributes_options_id"
                        class="form-control attributes_form"
                        name="attributes_options_id"
                        hx-get="api_h.php"
                        hx-vals='<?= '{"productsid": "' . (int)$products_id . '", "action": "product_attributes_getValueList"}' ?>'
                        hx-target="#attributes_values_id"
                        hx-trigger="change"
                    >
                        <option disabled="" selected="" value="">Options</option>';
                        <?php foreach ($options as $option) {?>
                            <option name="attributes_<?= $option['products_options_name'] ?>" value="<?= $option['products_options_id'] ?>"<?= ($attribute && $attribute['options_id'] == $option['products_options_id'] ? ' selected' : '') ?>><?= $option['products_options_name'] ?></option>';
                        <?php } ?>
                    </select>
                    <div id="attributes_values_id_container" class="form-group">
                        <select id="attributes_values_id" name="attributes_values_id" class="form-control attributes_form" data-current_id="-1">
                            <option disabled selected value>Values</option>
                            <?php
                               if ($attribute) {
                                    $values = tcs_get_options_values_select($attribute['options_id'], [$attribute['options_values_id']], $products_id);
                                    foreach ($values as $value) {
                                        echo '<option value="' . $value['value'] . '" ' . ( $value['selected'] ? 'selected' : '' ) . '>' . $value['name'] . '</option>';
                                    }
                               }

                            ?>
                        </select>
                    </div>
                    <div id="attributes_values_id_btns_container" class="btn-group"></div>
                </div>

                <div id="attributes_misc_container" class="form-inline form-group">

                    <div class="checkbox attributes_form"><label><input
                        type="checkbox"
                        name="attribute_default"
                        id="attribute_default"
                        <?= $attribute['attribute_default'] ? 'checked' : '' ?>
                        >&nbsp;Default</label></div>
                </div>
            </div>

            <div id="attributes_dependson_container" class="col-xs-5">
                <label>Depends On: </label>
                <select id="attributes_dependson_options_id"
                        name="attributes_dependson_options_id"
                        hx-vals='<?= '{"productsid": "' . (int)$products_id . '", "action": "product_attributes_getDependsOnValueList"}' ?>'
                class="form-control attributes_form"
                hx-get="api_h.php"
                hx-include="this"
                hx-target="#attributes_dependson_values_id"
                hx-trigger="change">
                <option disabled selected value>Options</option>
                <option>None</option>';
                <?php
                $attributes = $products_attributes->get_attributes();
                print_rr($attributes,'products_attributes');
                print_rr($attribute,'attribute');
                if($products_attributes) {
                    foreach ( $attributes  as $key => $option ) {?>
                        <option name="attributes_dependson_<?= $option['products_options_name'] ?>" value="<?= $key ?>" <?= ($attribute && $attribute['dependson_options_id'] == $key ? 'selected' : '') ?>><?= $option['products_options_name'] ?></option>';
                    <?php }
                }?>
                </select>
                <select multiple id="attributes_dependson_values_id" name="attributes_dependson_values_id[]" class="form-control attributes_form">
                    <?php
                    if ($attribute) {
                        $selected_values = strpos($attribute['dependson_options_values_id'], ',') ? explode(',', $attribute['dependson_options_values_id']) : [$attribute['dependson_options_values_id']];
                        $values = tcs_get_options_values_select($attribute['dependson_options_id'],$selected_values, $products_id);
                        foreach ($values as $value) {
                            echo '<option value="' . $value['value'] . '" ' . ( $value['selected'] ? 'selected' : '' ) . '>' . $value['name'] . '</option>';
                        }
                    }
                    ?>
                </select>
                <div id="attributes_dependson_values_id_btns_container" class="btn-group"></div>
            </div>
            <div id="attributes_submit" class="col-xs-1 form-group">
                <label>Save: </label>
                <button id="attributes_insert_btn"
                    class="btn btn-primary pull-right"
                    type="button"
                    hx-get="api_h.php"
                    hx-include=".attributes_form"
                    hx-vals='<?= '{ "action": "product_attributes_addToProduct","products_id": "' . (int)$products_id . '", "products_attributes_id": "' . (int)$products_attributes_id . '"}' ?>'
                    hx-trigger="click"
                    hx-target="#attributesTable_body"
                    hx-swap="beforeend"
                    hx-indicator="#indicatorLines"
                >Insert</button>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Get detailed explanation for a specific dependency issue type
 *
 * @param string $issue_type The type of issue
 * @return array Array containing title, description, and resolution steps
 */
function get_dependency_issue_explanation($issue_type) {
    $explanations = [
        'missing_dependency' => [
            'title' => '🔗 Missing Dependencies',
            'description' => 'This attribute depends on another attribute that doesn\'t exist in the product. When an attribute has a dependency, the dependent attribute must be present for the dependency to work correctly.',
            'resolution' => [
                '1. Check the "Depends On" setting for this attribute',
                '2. Either add the missing dependent attribute to the product',
                '3. Or remove the dependency by setting "Depends On" to "None"',
                '4. Verify that the dependent attribute has the correct option and value'
            ]
        ],
        'circular_dependency' => [
            'title' => '🔄 Circular Dependencies',
            'description' => 'Two or more attributes depend on each other in a loop, creating a circular dependency. This makes it impossible to determine the correct order of selection.',
            'resolution' => [
                '1. Identify all attributes in the dependency loop',
                '2. Break the loop by removing one of the dependencies',
                '3. Reorganize dependencies to flow in one direction only',
                '4. Consider if some dependencies are actually unnecessary'
            ]
        ],
        'sort_order_violation' => [
            'title' => '📊 Sort Order Violations',
            'description' => 'An attribute depends on another attribute that appears later in the sort order. Dependencies should always appear before the attributes that depend on them.',
            'resolution' => [
                '1. Adjust the sort order so dependent attributes come first',
                '2. The attribute being depended on should have a lower sort order number',
                '3. Review all sort orders to ensure logical dependency flow',
                '4. Test the attribute selection order in the frontend'
            ]
        ],
        'missing_variation' => [
            'title' => '🎯 Missing Variations',
            'description' => 'This attribute exists but has no corresponding product variations. Attributes should have at least one variation that uses them.',
            'resolution' => [
                '1. Create variations that include this attribute',
                '2. Or remove the attribute if it\'s no longer needed',
                '3. Check if variations were accidentally deleted',
                '4. Verify that the attribute is properly configured'
            ]
        ],
        'default_conflict' => [
            'title' => '⚠️ Default Conflicts',
            'description' => 'A default attribute depends on another attribute, but the current default for the dependency conflicts with this relationship.',
            'resolution' => [
                '1. Review which attributes are set as default',
                '2. Ensure default attributes don\'t conflict with dependencies',
                '3. Adjust default settings to match dependency requirements',
                '4. Consider removing default status from conflicting attributes'
            ]
        ],
        'self_option_group_dependency' => [
            'title' => '🚫 Self Option Group Dependencies',
            'description' => 'An attribute depends on another attribute from the same option group. This creates a logical impossibility since only one value from an option group can be selected at a time.',
            'resolution' => [
                '1. Remove the dependency on the same option group',
                '2. Create dependencies on attributes from different option groups',
                '3. Review the logical flow of attribute selection',
                '4. Consider splitting complex options into separate groups'
            ]
        ],
        'variation_missing_attribute' => [
            'title' => '❌ Missing Attributes',
            'description' => 'A product variation references attributes that don\'t exist in the product. This can happen when attributes are deleted but variations still reference them.',
            'resolution' => [
                '1. Add the missing attributes back to the product',
                '2. Or delete/update the variation to use existing attributes',
                '3. Check for recently deleted attributes',
                '4. Verify variation configuration is correct'
            ]
        ],
        'variation_duplicate_model' => [
            'title' => '📋 Duplicate Model Numbers',
            'description' => 'Multiple variations have the same model number. Each variation should have a unique model number for proper identification and inventory management.',
            'resolution' => [
                '1. Review all variation model numbers',
                '2. Assign unique model numbers to each variation',
                '3. Follow your company\'s model numbering convention',
                '4. Update any external systems that reference these models'
            ]
        ],
        'variation_missing_autodesk_link' => [
            'title' => '🔗 Missing Autodesk Links',
            'description' => 'This Autodesk product variation is missing a link to the Autodesk catalog. Autodesk products should be linked to their catalog entries for proper integration.',
            'resolution' => [
                '1. Find the correct Autodesk catalog entry',
                '2. Add the Autodesk catalog link to this variation',
                '3. Verify the link is valid and active',
                '4. Test the integration with Autodesk systems'
            ]
        ],
        'variation_invalid_autodesk_link' => [
            'title' => '❌ Invalid Autodesk Links',
            'description' => 'This variation has an Autodesk catalog link that doesn\'t exist or is invalid. This can break integration with Autodesk systems.',
            'resolution' => [
                '1. Verify the Autodesk catalog link is correct',
                '2. Update with a valid catalog entry',
                '3. Remove the link if the product is no longer in the catalog',
                '4. Contact Autodesk support if catalog issues persist'
            ]
        ],
        'variation_selection_issue' => [
            'title' => '⚡ Selection Issues',
            'description' => 'This variation is enabled but uses attributes that are disabled or have other issues. This can cause problems with product selection and ordering.',
            'resolution' => [
                '1. Check if all attributes used by this variation are enabled',
                '2. Disable the variation if its attributes are problematic',
                '3. Fix the underlying attribute issues first',
                '4. Re-enable the variation once attributes are resolved'
            ]
        ]
    ];

    return $explanations[$issue_type] ?? [
        'title' => '❓ Unknown Issue',
        'description' => 'An unknown issue type was encountered. This may indicate a system error or a new type of issue that needs to be addressed.',
        'resolution' => [
            '1. Contact system administrator',
            '2. Report this issue type: ' . $issue_type,
            '3. Provide details about when this occurred',
            '4. Check system logs for additional information'
        ]
    ];
}


function tcs_get_options_values_select($options_id, $selected_values = [], $products_id = null):array {
    global $languages_id;

    $option_values = [];

    $products_id_whore_sql = $products_id ? " AND pa.products_id = " . (int)$products_id : '';
    $products_id_JOIN_sql = $products_id ? " LEFT JOIN products_attributes pa  on pa.options_values_id = pov.products_options_values_id " : '';

    $query = "SELECT pov.products_options_values_id, pov.products_options_values_name
                  FROM products_options_values pov
                        LEFT JOIN products_options_values_to_products_options povtpo on povtpo.products_options_values_id = pov.products_options_values_id 
                       $products_id_JOIN_sql
                    WHERE povtpo.products_options_id = '" . (int)$options_id . "'" .
                        $products_id_whore_sql .
                       " AND pov.language_id = '" . (int)$languages_id . "'
                    ORDER BY pov.products_options_values_name";
    print_rr($query,'query');
    $values = tcs_db_query($query);
    foreach ($values as $value) {
        $option_values[] = [
            'value' => $value['products_options_values_id'],
            'name' => $value['products_options_values_name'],
            'selected' => in_array($value['products_options_values_id'], $selected_values)
        ];
    }
    print_rr(['options_id' => $options_id, 'products_id' => $products_id, 'values' => $values,  'option_values' => $option_values],'option_values');
    return $option_values;
}


function tcs_draw_variations_table($products_attributes, $products_id, $body_only = false, $params = []){
    // Define columns for the table
    $columns = [
        ['name' => '&nbsp;', 'class' => '', 'params' => ''],
        ['name' => 'Model', 'class' => '', 'params' => ''],
        ['name' => 'GTIN', 'class' => '', 'params' => ''],
        ['name' => 'Image ID', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Price', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Options', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Sort Order', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Autodesk Link', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Actions', 'class' => 'text-center', 'params' => ['colspan' => '2']],
    ];

    // Get the variations
    $variations = $products_attributes->get_variations();
    //print_rr($products_attributes);
    $rows = '';
    foreach ($variations as $key_a => $variation) {
        $rows .= tcs_draw_variations_table_row($variation);
    }

    // If body_only is true, return just the rows
    if ($body_only) {
        return $rows;
    }

    $footer = "<div class='panel-footer varitionsFooter' x-data='formElement()' x-show='show' x-transition:enter='form-enter-active' x-transition:enter-start='form-enter' x-transition:enter-end='form-enter-to' x-transition:leave='form-leave-active' x-transition:leave-start='form-leave' x-transition:leave-end='form-leave-to'>
        <div id='variations_options_container' class=''>
            <div class='row'>";
    $footer .= tcs_draw_variations_form($products_attributes, $products_id);
    $footer .= "</div></div></div>";

    // Output the table with HTMX attributes for sort order updates
    return tcs_draw_admin_bootstrap_table(
        'Variations Table',
        'variationsTable',
        'table table-striped table-draggable',
        $columns,
        $rows,
        $footer,
        ['body' => ['hx-get' => 'api_h.php', 'hx-include' => '.variationsSOinput', 'hx-target' => '#variations_error_message','hx-vals' => '{"action": "product_variations_updateSortOrder", "products_id": "' . $products_id . '"}', 'hx-trigger' => 'deactivate'] ],
        $body_only
    );
}


function tcs_draw_variations_table_row($variation,$params = []){
    print_rr($variation,'variations');
    $attributes = explode('{', substr($variation['attribute_string'], strpos($variation['attribute_string'], '{') + 1));
    $attributesNameString = "";
    for ($i = 0, $n = sizeof($attributes); $i < $n; $i++) {
        $pair = explode('}', $attributes[$i]);
        $attributesNameString .= '<span style="font-weight:bold">' . tep_options_name($pair[0]) . ':</span> ';
        $attributesNameString .= tep_values_name($pair[1]) . ' ';
    }

    $rowId = $variation['products_variations_id'];
    $rowClass = ($variation['enabled'] == "0") ? "table-danger danger" : "variation_row";
    $autodesk_link = "";
    $autodesk_link = $variation['autodesk_link_name'];
    if ($variation['autodesk_link'] != "") {
        $autodesk_link = $autodesk_link;
    }
    // Add this row's data to the dataset
    $id = "variations_table_row_{$rowId}";

    $row_content = [
        ['class' => 'portletTD',          'id' => '',                                     'content' => '<div class="portlet">&nbsp;</div>'],
        ['class' => '',                   'id' => "variations_table_model_{$rowId}",      'content' => $variation['model']],
        ['class' => '',                   'id' => "variations_table_gtin_{$rowId}",       'content' => $variation['gtin']],
        ['class' => 'text-center',        'id' => "variations_table_image_id_{$rowId}",   'content' => $variation['image_id']],
        ['class' => 'text-center',        'id' => "variations_table_Price_{$rowId}",      'content' => $variation['price']],
        ['class' => '',                   'id' => "variations_table_attributes_{$rowId}", 'content' => $attributesNameString],
        ['class' => '',                   'id' => '',                                     'content' => '<input class="indexSO variationsSOinput" type="text" size="1" maxlength="4" value="' . $variation['sort_order'] . '" name="variationsTableSO[' . (int)$variation['products_variations_id'] . ']">'],
        ['class' => '',                   'id' => '',                                     'content' => $autodesk_link],
        ['class' => 'listsOptionsEdit',   'id' => '',                                     'content' => 'e', 'hx-target' => '#variations_form',
            'hx-get'=> 'api_h.php?action=product_variations_product_edit',
            'hx-vals' => '{"products_id":"' . $variation['products_id'] . '", "products_variations_id":"' . $variation['products_variations_id'] . '"}'],
        ['class' => 'listsOptionsDelete', 'id' => '',                                     'content' => 'x', 'hx-target' => "#{$id}",
            'hx-get'=> 'api_h.php?action=product_variations_removeVariation',
            'hx-vals' => '{"products_variations_id":"' . $variation['products_variations_id'] . '"}'],
    ];
    //print_rr($params,'paramamaamama');

    // Add Alpine.js transition directives for smooth animations
    $alpine_params = [
        'x-data' => 'tableRow()',
        'x-show' => 'show',
        'x-transition:enter' => 'table-row-enter-active',
        'x-transition:enter-start' => 'table-row-enter',
        'x-transition:enter-end' => 'table-row-enter-to',
        'x-transition:leave' => 'table-row-leave-active',
        'x-transition:leave-start' => 'table-row-leave',
        'x-transition:leave-end' => 'table-row-leave-to'
    ];

    $params = array_merge($params, $alpine_params);
    $params['data-variationsid'] = $rowId;
    // Output the table
    return tcs_draw_admin_bootstrap_table_row($id, $rowClass, $row_content,$params);
}


function tcs_draw_variations_form($id, $products_id, $values = []) {
    $products_options_name_query = tep_db_query("select distinct popt.products_options_id, popt.products_options_name from products_options popt, products_attributes patrib where patrib.products_id='" . $products_id . "' and patrib.options_id = popt.products_options_id order by popt.products_options_name");
    $select_output_selects = [
        "<common>" => [
            "class" => "form-control variations_form"
        ]
    ];
    if (tep_db_num_rows($products_options_name_query)) {
        while ($products_options_name = tep_db_fetch_array($products_options_name_query)) {
            $products_options_query = tep_db_query("select pov.products_options_values_id, pov.products_options_values_name, pa.options_values_price, pa.price_prefix, pa.attribute_default, pa.dependson_options_id, pa.dependson_options_values_id from products_attributes pa, products_options_values pov where pa.products_id = '" . $products_id . "' and pa.options_id = '" . (int)$products_options_name['products_options_id'] . "' and pa.options_values_id = pov.products_options_values_id order by pa.products_attributes_sort_order");
            $select_id = "attribute[{$products_options_name['products_options_id']}]";
            $select_output_selects[$select_id] = [
                "node_type" => "select",
                "label" => $products_options_name['products_options_name'],
                "options" => []
            ];
            while ($products_options = tep_db_fetch_array($products_options_query)) {
                $select_output_selects[$select_id]['options'][$products_options['products_options_values_id']] = $products_options['products_options_values_name'];
            }
        }
    }


    $array = [
        "form" => [
            "<cfg>" => [
                "common" => [
                    "name" => "name_<node_type>",
                    "id" => "<name>_<node_type>",
                    "label" => "<name>",
                    "value" => "<values_list>"
                ],
                "noTag" => true,
                "id" => "variations_form",
                "class" => "variations_form",
                "x-data" => "formElement()",
                "x-show" => "show",
                "x-transition:enter" => "form-enter-active",
                "x-transition:enter-start" => "form-enter",
                "x-transition:enter-end" => "form-enter-to",
                "x-transition:leave" => "form-leave-active",
                "x-transition:leave-start" => "form-leave",
                "x-transition:leave-end" => "form-leave-to"
            ],
            "inputs" => [
                "node_type" => "group",
                "class" => "col-sm-3 well",
                "elements" => [
                    "<common>" => [
                        "node_type" => "input",
                        "class" => "form-control variations_form",
                    ],
                    "model" => [],
                    "GTIN" => [],
                    "image_id" => [],
                    "price" => [],
                    "products_id" => [
                        "type" => "hidden",
                        "value" => $products_id
                    ]
                ]
            ],
            "right_elements" => [
                "node_type" => "group",
                "class" => "col-sm-9",
                "elements" => [
                    "attributes" => [
                        "node_type" => "group",
                        "class" => "col-sm-6 well",
                        "elements" => $select_output_selects
                    ],
                    "autodesk_linking" => [
                        "node_type" => "group",
                        "class" => "col-sm-6 well",
                        "elements" => [
                            "<common>" => [
                                "class" => "form-control variations_form"
                            ],
                            "terms" => [
                                "node_type" => "input",
                                "label" => ""
                            ],
                            "search" => [
                                "node_type" => "button",
                                "hx-trigger" => "click",
                                "hx-get" => "api_h.php?action=product_autodesk_link_search",
                                "hx-target" => "#autodesk_link_select",
                                "hx-include" => "#terms_input",
                                "hx-swap" => "innerHTML"
                            ],
                            "autodesk_link" => [
                                "node_type" => "select"
                            ]
                        ]
                    ]
                ]
            ],
            "controls" => [
                "node_type" => "group",
                "class" => "col-sm-9 well text-right",
                "elements" => [
                    "submit" => [
                        "node_type" => "button",
                        "hx-trigger" => "click",
                        "hx-get" => "api_h.php?action=product_save",
                        "hx-indicator" => "#indicatorLines",
                        "hx-include" => ".variations_form",
                        "hx-target" => "#variations_table_body",
                        "hx-swap" => "beforeend"
                    ],
                    "cancel" => [
                        "node_type" => "button",
                        "hx-trigger" => "click",
                        "hx-get" => "api_h.php?action=product_cancel",
                        "hx-indicator" => "#indicatorLines",
                        "hx-include" => ".variations_form",
                        "hx-target" => "#variations_form",
                        "hx-swap" => "outerHTML"
                    ]
                ]
            ]
        ]
    ];
    ////print_rr($array,"renderarray");
    return renderForm($array, $values);

}

