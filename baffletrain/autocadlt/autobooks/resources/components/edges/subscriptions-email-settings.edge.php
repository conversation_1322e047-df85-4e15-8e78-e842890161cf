@props([
    'title' => 'Email Settings',
    'description' => 'Email template settings and configuration'
])

@php
include_once 'resources/functions/func_email_settings.php';

$file_path = FS_APP_ROOT . 'resources/views/subscriptions/email_history/reminder_email.view.php';
$email_template = file_get_contents($file_path);
if ($email_template === false) {
    throw new Exception("Failed to load email template from {$file_path}");
}

$lines = explode("\n", $email_template);
$from = trim($lines[0]);
$subject = trim($lines[1]);
$email_body = implode("\n", array_slice($lines, 2));

$email_rules = explode(',', autodesk_api::database_get_storage('subscription_renew_email_send_rules'));
$settings_days = autodesk_api::database_get_storage('subscription_renew_email_send_days');
$settings_time = autodesk_api::database_get_storage('subscription_renew_email_send_time');

$subjectFields = [
    'endCustomerName' => 'End Customer Name',
    'product_name' => 'Product Name',
    'term' => 'Term',
    'seats' => 'Seats',
    'subscriptionReferenceNumber' => 'Subscription Reference Number',
    'opportunityNumber' => 'Opportunity Number',
    'endCustomerCsn' => 'End Customer CSN',
    'status' => 'Status',
    'startDate' => 'Start Date',
    'endDate' => 'End Date'
];
@endphp

<x-layout-card id="email_settings_card">
    <x-forms-input
        type="text"
        name="email_from"
        label="From"
        :value="$from"
        class-suffix="email_form"
    />
    
    <x-forms-input
        type="text"
        name="email_subject"
        label="Subject"
        :value="$subject"
        class-suffix="email_form"
    />

    <x-forms-select
        name="fields"
        label="Subject fields"
        :options="$subjectFields"
    />
    <x-forms-textarea
        id="email_template"
        name="email_template"
        label=""
        :content="$email_body"
        rows="25"
        class-suffix="email_form"
    />
    <div class="grid grid-cols-12 grid-rows-2 gap-2 sm:gap-2 md:gap-4 lg:gap-6 xl:gap-8 justify-end items-end">
        <div class="col-span-6">
            <label for="email_rules" class="block text-sm/6 font-medium text-gray-900">Days to send before/after expiration: </label>
            <div>
                <x-email-send-rules-widget :rules="$email_rules" />
            </div>
        </div>

        <div class="col-span-3" 
             x-data="{
                 selectedDays: [{{ $settings_days }}],
                 toggleDay(day) {
                     this.selectedDays[day] = !this.selectedDays[day];
                 }
             }">
            <label for="days_to_send" class="block text-sm/6 font-medium text-gray-900">Days To Send: </label>
            <div class="isolate inline-flex rounded-md shadow-sm" 
                 hx-post="{{ APP_ROOT }}/api/update_days_to_send" 
                 hx-include="#email_send_days" 
                 hx-trigger="click delay:2000ms" 
                 hx-swap="none">
                <input type="hidden" x-model="selectedDays" id="email_send_days" name="days_to_send" value="">
                @php
                    $days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
                @endphp
                @foreach($days as $key => $day)
                    <button type="button"
                            name="day_{{ $day }}"
                            class="email_send_days {{ $key === 0 ? 'relative inline-flex items-center rounded-l-md' : ($key === count($days) - 1 ? 'relative -ml-px inline-flex items-center rounded-r-md' : 'relative -ml-px inline-flex items-center') }} bg-white px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-10"
                            :class="{'bg-blue-500 text-white': selectedDays[{{ $key }}]}"
                            @click="toggleDay({{ $key }})"
                            x-bind:value="selectedDays[{{ $key }}]">{{ $day }}</button>
                @endforeach
            </div>
        </div>

        <div class="col-span-3">
            <div x-data="{ currentVal: {{ $settings_time }} }" class="w-full items-center text-neutral-600 dark:text-neutral-300">
                <label for="hour_of_day" class="block text-sm/6 font-medium text-gray-900">Hour of day to send: </label>
                <div class="flex items-center">
                    <input class="block text-sm/6 font-medium text-gray-900" 
                           x-model="currentVal" 
                           id="hour_of_day" 
                           name="time_to_send" 
                           type="range" 
                           min="0" 
                           max="23" 
                           step="1" 
                           hx-trigger="change delay 1000ms" 
                           hx-post="{{ APP_ROOT }}api/update_time_to_send">
                    <span class="ml-2 text-lg font-bold text-neutral-900 text-black" x-text="currentVal < 10 ? '0' + currentVal : currentVal">00</span>
                    <span class="text-lg font-bold text-neutral-900 text-black">:00</span>
                </div>
            </div>
        </div>
        @php
            $input = json_encode([
                    'id' => 'test_email_to',
                    'name' => 'test_email_to',
                    'icon' => 'envelope'
            ]);
            $button = json_encode([
                    'id' => 'test_email_send',
                    'name' => 'test_email_send',
                    'label' => 'Send',
                    'icon' => 'envelope',
                    'hx-post' => APP_ROOT . 'api/send_test_email',
                    'hx-swap' => 'this',
                    'hx-target' => 'this',
                    'hx-include' => '#test_email_to'
            ])
        @endphp
        <div class="col-span-4">
            <x-forms-input-button-group
                label="Send test email"
                :input="$input"
                :button="$button"
            />
        </div>
    </div>
    <script>
        Jodit.defaultOptions.controls.insertPlaceholder = {
            tooltip: 'Insert Placeholder', // Tooltip for the dropdown button
            icon: 'plus', // Icon for the dropdown button
            list: {
                '@{{endCustomerName}}': 'End Customer Name',
                '@{{product_name}}': 'Product Name',
                '@{{term}}': 'Term',
                '@{{seats}}': 'Seats',
                '@{{subscriptionReferenceNumber}}': 'Subscription Reference Number',
                '@{{opportunityNumber}}': 'Opportunity Number',
                '@{{endCustomerCsn}}': 'End Customer CSN',
                '@{{status}}': 'Status',
                '@{{startDate}}': 'Start Date',
                '@{{endDate}}': 'End Date'
            },
            exec(editor, _, {
                control
            }) {
                value = control.args && control.args[0]; // Get the selected placeholder
                if (value) {
                    editor.s.insertHTML(value); // Insert the selected placeholder into the editor
                }
            }
        };

        // Initialize the Jodit editor with the custom dropdown button
        emailEditor = Jodit.make('#email_template', {
            buttons: [...Jodit.defaultOptions.buttons, 'insertPlaceholder'] // Add the dropdown button to the toolbar
        });
    </script>

</x-layout-card>