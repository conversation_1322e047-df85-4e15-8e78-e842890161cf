/* Welsh/UK initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON>. */
jQuery(function($){
	$.datepicker.regional['cy-GB'] = {
		closeText: 'Done',
		prevText: 'Prev',
		nextText: 'Next',
		currentText: 'Today',
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON><PERSON><PERSON>','Awst','<PERSON><PERSON>','<PERSON>yd<PERSON><PERSON>','<PERSON><PERSON>wedd','Rhagfyr'],
		monthNamesShort: ['<PERSON>', 'Chw', 'Maw', 'Ebr', '<PERSON>', 'Meh',
		'Gor', 'Aws', 'Med', 'Hyd', 'Tac', 'Rha'],
		dayNames: ['<PERSON>ydd <PERSON>', '<PERSON>ydd <PERSON>lun', '<PERSON>ydd <PERSON>rth', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>w<PERSON>'],
		dayNamesShort: ['<PERSON>', '<PERSON><PERSON>', 'Maw', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>'],
		dayNamesMin: ['<PERSON>','Ll','Ma','Me','Ia','Gw','Sa'],
		weekHeader: 'Wy',
		dateFormat: 'dd/mm/yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['cy-GB']);
});
