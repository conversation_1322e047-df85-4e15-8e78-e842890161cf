<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Alpine.js Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
    
    <style>
        body {
            padding: 20px;
        }
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1050;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal-content {
            background: white;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
            padding: 20px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Alpine.js Modal Test</h1>
        
        <div x-data="{ show: true, modals: {} }">
            <div x-show="show" x-transition>
                <div class="alert alert-warning">
                    <h4>Test Issue:</h4>
                    <ul>
                        <li>
                            🔗 This is a test dependency issue
                            <button class="btn btn-info btn-xs" 
                                    @click="modals.test_modal = true"
                                    style="margin-left: 8px;">ℹ️</button>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Test Modal -->
            <div x-show="modals.test_modal" 
                 x-transition
                 @click="modals.test_modal = false"
                 class="modal-overlay">
                
                <div @click.stop="" class="modal-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h3 style="margin: 0;">🔗 Test Issue</h3>
                        <button @click="modals.test_modal = false" 
                                style="background: none; border: none; font-size: 24px; cursor: pointer;">
                            &times;
                        </button>
                    </div>
                    
                    <div>
                        <h4>What this means:</h4>
                        <p>This is a test modal to verify Alpine.js is working correctly.</p>
                        
                        <h4>How to resolve:</h4>
                        <div>
                            <div>1. Click the close button</div>
                            <div>2. Or click outside the modal</div>
                            <div>3. Verify the modal closes properly</div>
                        </div>
                    </div>
                    
                    <div style="text-align: right; margin-top: 20px;">
                        <button @click="modals.test_modal = false" class="btn btn-secondary">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info" style="margin-top: 20px;">
            <h4>Test Instructions:</h4>
            <ol>
                <li>Click the blue ℹ️ button above</li>
                <li>A modal should open with detailed information</li>
                <li>Try closing it by clicking the X, Close button, or outside the modal</li>
                <li>If this works, the Alpine.js setup is correct</li>
            </ol>
        </div>
    </div>
    
    <script>
        // Debug Alpine.js
        document.addEventListener('alpine:init', () => {
            console.log('Alpine.js initialized successfully');
        });
        
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
            alert('JavaScript Error: ' + e.error.message);
        });
    </script>
</body>
</html>
