<?php
/**
 * Test script to verify the paths fix
 */

echo "Testing paths fix...\n";

try {
    // Simulate the same environment as startup_sequence_minimal.php
    define('DEBUG_MODE', true);
    define('API_RUN', true);

    // Include necessary files
    $path = ['fs_app_root' => __DIR__ . '/'];

    // Load the path schema
    $schema = require_once $path['fs_app_root'] . 'system/config/path_schema.php';

    require_once $path['fs_app_root'] . 'system/paths.php';
    
    // Set up minimal path data
    $path['doc_root'] = __DIR__ . '/';
    $path['fs_doc_root'] = __DIR__ . '/';
    
    // Set up minimal server variables to prevent undefined index errors
    $_SERVER['REQUEST_URI'] = $_SERVER['REQUEST_URI'] ?? '/test';
    $_SERVER['SERVER_NAME'] = $_SERVER['SERVER_NAME'] ?? 'localhost';
    $_SERVER['SCRIPT_NAME'] = $_SERVER['SCRIPT_NAME'] ?? '/test.php';
    $_SERVER['SCRIPT_FILENAME'] = $_SERVER['SCRIPT_FILENAME'] ?? __FILE__;
    $_SERVER['DOCUMENT_ROOT'] = $_SERVER['DOCUMENT_ROOT'] ?? __DIR__;
    
    echo "Calling build_paths with schema...\n";
    $path = build_paths($path, $schema);
    
    echo "Success! build_paths completed without errors.\n";
    echo "Generated paths:\n";
    foreach ($path as $key => $value) {
        if (is_string($value)) {
            echo "  $key: $value\n";
        } else {
            echo "  $key: " . gettype($value) . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "Fatal Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nTest completed.\n";
?>
