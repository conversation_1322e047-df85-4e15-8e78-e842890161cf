<?php
use edge\Edge;

// Display the quote form UI with Alpine.js and HTMX
echo '<div id="quote_form_ui_alpine_htmx_container" class="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">';
echo '<div class="mx-auto max-w-4xl">';
echo '<h1 class="text-2xl font-bold leading-tight tracking-tight text-gray-900 mb-6">Autodesk Quote V3 Form</h1>';
echo '<p class="mb-6 text-gray-600">This form uses Alpine.js for reactivity and HTMX for server interactions. Field descriptions are hidden by default and shown in a popup when you click the info button.</p>';

// Display the form using the Edge template
echo Edge::render('quote-v3-ui-form', [
    'endpoint' => 'api/quotes/submit',
    'schema_path' => 'resources/schemas/QuoteV3.json',
    'title' => 'Create Autodesk Quote',
    'description' => 'This form uses Alpine.js for reactivity and HTMX for server interactions. Field descriptions are hidden by default and can be shown by clicking the help button.'
]);

echo '</div>';
echo '</div>';
