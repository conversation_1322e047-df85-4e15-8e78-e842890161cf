<?php namespace edgeTemplate\view_031825233552ac096e3ad18d400c2148;use edge\Edge;use edge\loop;?><?php extract(['edge_manifest' => array (
)]);$loop = new Loop(0);print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){ ?>@layout('layout-main')

@section('title', 'CSV Database Demo')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">CSV Database Import Demo</h1>
        <p class="text-gray-600 mt-2">Upload a CSV file and automatically create a database table for better performance and querying</p>
    </div>

    <div class="grid gap-8 lg:grid-cols-2">
        
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Upload CSV File</h2>
            <?= Edge::render('templates-csv-uploader', ["title" => "CSV File Upload", "description" => "Upload a CSV file to automatically create a database table", 'max_file_size' => 10], 0) ?>
        </div>

        
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">How It Works</h2>
            
            <div class="space-y-4">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-blue-600">1</span>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">Upload CSV</h3>
                        <p class="text-sm text-gray-600">Select and upload your CSV file using the form above.</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-blue-600">2</span>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">Automatic Analysis</h3>
                        <p class="text-sm text-gray-600">The system analyzes your CSV headers and data types automatically.</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-blue-600">3</span>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">Database Table Creation</h3>
                        <p class="text-sm text-gray-600">A database table is created with appropriate column types.</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-blue-600">4</span>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">Data Import</h3>
                        <p class="text-sm text-gray-600">Your CSV data is imported into the database table.</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-blue-600">5</span>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">Enhanced Features</h3>
                        <p class="text-sm text-gray-600">Enjoy pagination, searching, sorting, and better performance.</p>
                    </div>
                </div>
            </div>

            <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-md">
                <h3 class="text-sm font-medium text-green-800">Benefits</h3>
                <ul class="mt-2 text-sm text-green-700 space-y-1">
                    <li>• Better performance for large datasets</li>
                    <li>• Full-text search capabilities</li>
                    <li>• Efficient pagination</li>
                    <li>• Column-based sorting</li>
                    <li>• Persistent data storage</li>
                </ul>
            </div>
        </div>
    </div>

    
    <div class="mt-8 bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-900">Existing CSV Tables</h2>
            <a href="/csv-viewer" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                View All Tables
            </a>
        </div>
        
        <?php
        $tables_result = csv_database_manager::list_tables();
        ?>
        
        <?php if(isset($tables_result['success']) && $tables_result['success'] && !empty($tables_result['tables'])): ?>
            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                <?php if (is_countable(array_slice($tables_result['tables'], 0, 6))) { $loop = new loop(count(array_slice($tables_result['tables'], 0, 6)),$loop->depth+1,$loop); foreach(array_slice($tables_result['tables'], 0, 6) as $table): ?>
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <h3 class="font-medium text-gray-900 truncate"><?= $table ?></h3>
                        <div class="mt-2 flex items-center space-x-2">
                            <a href="/csv-viewer?table=<?= $table ?>" 
                               class="text-sm text-blue-600 hover:text-blue-800">
                                View
                            </a>
                            <span class="text-gray-300">•</span>
                            <button 
                                onclick="if(confirm('Delete <?= $table ?>?')) { deleteTable('<?= $table ?>'); }"
                                class="text-sm text-red-600 hover:text-red-800">
                                Delete
                            </button>
                        </div>
                    </div>
                <?php $loop->update(); endforeach; $loop = $loop->parent; } ?>
            </div>
            
            <?php if(count($tables_result['tables']) > 6): ?>
                <div class="mt-4 text-center">
                    <a href="/csv-viewer" class="text-sm text-blue-600 hover:text-blue-800">
                        View all <?= count($tables_result['tables']) ?> tables →
                    </a>
                </div>
            <?php endif ?>
        <?php else: ?>
            <div class="text-center py-8 text-gray-500">
                <div class="text-lg font-medium">No CSV Tables Found</div>
                <div class="text-sm mt-1">Upload a CSV file above to get started.</div>
            </div>
        <?php endif ?>
    </div>
</div>

<script>
async function deleteTable(tableName) {
    try {
        const formData = new FormData();
        formData.append('table_name', tableName);
        
        const response = await fetch('/api/csv/delete_table', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Table deleted successfully');
            location.reload();
        } else {
            alert('Error deleting table: ' + (result.error || 'Unknown error'));
        }
    } catch (error) {
        alert('Network error: ' + error.message);
    }
}
</script>
@endsection
<?php } ?>