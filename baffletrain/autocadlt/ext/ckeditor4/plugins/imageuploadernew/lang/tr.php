<?php

$imagebrowser1 = "Image Browser for CKEditor";
$imagebrowser2 = "Toplam:";
$imagebrowser3 = "Resim";
$imagebrowser4 = "Dosyaları buraya sürükleyin.";

$uploadpanel1 = "Lütfen bir dosya seçin:";
$uploadpanel2 = "Resim buraya yüklendi:";
$uploadpanel3 = "Yükleme klasörünü ayarlardan değiştirebilirsiniz.";

$panelsettings1 = "Yükleme klasörü:";
$panelsettings2 = "Lütfen mevcut bir klasör seçin:";
$panelsettings3 = "Klasör geçmişi:";
$panelsettings4 = "Ayarlar:";
$panelsettings5 = "Eklentileri gizle";
$panelsettings6 = "Eklentileri göster";
$panelsettings7 = "Parola:";
$panelsettings8 = "Çıkış yap";
$panelsettings9 = "Parolayı kaldır";
$panelsettings10 = "Eklentiyi sevdiniz mi?";
$panelsettings11 = "Bir kahve ısmarlayın!";
$panelsettings12 = "Destek:";
$panelsettings13 = "S.S.S";
$panelsettings14 = "Sorun Bildir";
$panelsettings15 = "Versiyon:";
$panelsettings16 = "Geliştirici:";
$panelsettings17 = "Moritz Maleck";
$panelsettings18 = "İkonlar:";
$panelsettings19 = "Icons8 ikon paketi.";
$panelsettings20 = "Dili Değiştir";
$panelsettings21 = "Haber bölümünü gizle";
$panelsettings22 = "Haber bölümünü göster";

$newssection1 = "Haber bölümünü ayarlar panelinden devre dışı bırakabilirsiniz.";

$buttons1 = "Göster";
$buttons2 = "İndir";
$buttons3 = "Kullan";
$buttons4 = "Sil";
$buttons5 = "İptal";
$buttons6 = "Kaydet";
$buttons7 = "Yükle";

$alerts1 = "Image Uploader and Browser eklentisini seçtiğiniz için teşekkürler!";
$alerts2 = "Yükleme klasörünün chmod ayarlarının 777 (yazılabilir) olduğundan emin olun.";
$alerts3 = "Filezilla kullanarak dosya izinlerini değiştirmek için buraya tıklayın.";
$alerts4 = "Daha fazla bilgi için <a href='http://imageuploaderforckeditor.altervista.org/' target='_blank'>Dökümantasyon</a> yada <a href='http://imageuploaderforckeditor.altervista.org/support/' target='_blank'>S.S.S</a> sayfasına göz atın.";
$alerts5 = "Bu eklentiyi kullanmak için web tarayıcınızda JavaScript'i etkinleştirmeniz gerekir.";
$alerts6 = "Web tarayıcınızda JavaScript'i etkinleştirmek için:";
$alerts7 = "Yeni bir sürüm mevcut!";
$alerts8 = "Hemen indir";
$alerts9 = "Klasör";
$alerts10 = "bulunamadı.";
$alerts11 = "oluşturuldu.";

$dltimageerrors1 = "Bir sorun oluştu.";
$dltimageerrors2 = "Yalnızca resimleri silebilirsiniz. Lütfen bir resim dosyası seçerek tekrar deneyin.";
$dltimageerrors3 = "Silmek istediğiniz dosya bulunamadı.";
$dltimageerrors4 = "Sistem dosyalarını silemezsiniz. Lütfen bir resim dosyası seçerek tekrar deneyin.";
$dltimageerrors5 = "Bu dosyayı silemezsiniz. Lütfen bir resim dosyası seçerek tekrar deneyin.";
$dltimageerrors6 = "Silmek istediğiniz dosya mevcut değil. Lütfen bir resim dosyası seçerek tekrar deneyin.";

$uploadimgerrors1 = "Lütfen bir resim dosyası seçin.";
$uploadimgerrors2 = "Üzgünüz, bu dosya zaten yüklenmiş.";
$uploadimgerrors3 = "Üzgünüz, dosya boyutu çok büyük.";
$uploadimgerrors4 = "Üzgünüz, yalnızca JPG, JPEG, PNG ve GIF uzantılı resimleri yükleyebilirsiniz.";
$uploadimgerrors5 = "Üzgünüz, dosya yüklenemedi. Yükleme klasörünün chmod ayarlarının 0777 yazılabilir olduğundan emin olun.";
$uploadimgerrors6 = "Üzgünüz, bir sorun oluştu -";
$uploadimgerrors7 = "- Yükleme klasörünün chmod ayarlarının 0777 yazılabilir olduğundan emin olun.";

$loginerrors1 = "Kullanıcı bulunamadı. Kullanıcı adı ve parola hatalı.";

$configerrors1 = "Görünürlüğü değiştirmek için ayarlar kısmını kullanın veya tekrar deneyin.";
$configerrors2 = "Stilleri değiştirmek için ayarlar kısmını kullanın veya tekrar deneyin.";

$loginsite1 = "Hoşgeldiniz!";
$loginsite2 = "Lütfen giriş yapın.";
$loginsite3 = "Kullanıcı adı";
$loginsite4 = "Parola";
$loginsite5 = "Giriş yap";

$createaccount1 = "Başka kullanıcıların resimlerinizi görmesini engellemek için yeni bir hesap oluşturun.";
$createaccount2 = "Şifre korumasını nasıl kaldırabilirim ?";
$createaccount3 = "Varsayılan yükleme klasörü <b>ckeditor/plugins/imageuploader/uploads</b>. Ayarlardan değiştirebilirsiniz.";

$langpanel1 = "Lütfen dil seçin:";
$langpanel2 = "Seçili olan:";
$langpanel3 = "Kapat";
