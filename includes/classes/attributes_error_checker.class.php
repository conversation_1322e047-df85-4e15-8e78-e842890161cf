<?php

/**
 * Product Attributes Error Checker Class
 * 
 * This class handles all error checking functionality for product attributes,
 * including dependency validation, variation checks, and data integrity verification.
 */
class tcs_product_attributes_error_checker {
    
    private $attributes_instance;
    
    /**
     * Constructor
     * 
     * @param tcs_product_attributes $attributes_instance Instance of the main attributes class
     */
    public function __construct($attributes_instance) {
        $this->attributes_instance = $attributes_instance;
    }
    
    /**
     * Check for attribute dependency issues and variations problems
     *
     * Checks for:
     * - Missing dependencies in attributes
     * - Sort order violations in attributes
     * - Circular dependencies in attributes
     * - Missing variations for attributes
     * - Default attribute conflicts
     * - Self option group dependencies
     * - Missing attributes referenced in variations
     * - Duplicate model numbers in variations
     * - Missing or invalid Autodesk links in variations
     * - Selection issues (enabled variations with disabled attributes)
     *
     * @return array Array of issues found, indexed by attribute_id or variation_id. Each entry contains name, issues array, and optionally is_variation flag
     */
    public function check_dependency_issues() {
        if (!$this->attributes_instance->has_attributes) {
            return [];
        }
        if ($this->attributes_instance->interlinked || empty($this->attributes_instance->attributes)) {
            $this->attributes_instance->init(true);
        }

        print_rr( 'starting check_dependency_issues','attributes_instance_starting',true,true );
        $attributes = $this->attributes_instance->get_attributes(false);
        $issues_by_attribute = [];
        
        // Build attribute map for quick lookup
        $attribute_map = $this->build_attribute_map($attributes);

        // Collect all issues from different checks
        $all_issues = [];

        // Check for missing dependencies
        $all_issues = array_merge($all_issues, $this->check_missing_dependencies($attribute_map));

        // Check for sort order violations
        $all_issues = array_merge($all_issues, $this->check_sort_order_violations($attribute_map));

        // Check for circular dependencies
        $all_issues = array_merge($all_issues, $this->check_circular_dependencies($attribute_map));

        // Check for missing variations
        $all_issues = array_merge($all_issues, $this->check_missing_variations($attribute_map));

        // Check for default attribute conflicts
        $all_issues = array_merge($all_issues, $this->check_default_conflicts($attribute_map));

        // Check for self option group dependencies
        $all_issues = array_merge($all_issues, $this->check_self_option_group_dependencies($attribute_map));

        // Check variations-specific issues
        $all_issues = array_merge($all_issues, $this->check_variations_missing_attributes($attribute_map));
        $all_issues = array_merge($all_issues, $this->check_variations_duplicate_models());
        $all_issues = array_merge($all_issues, $this->check_variations_autodesk_links());
        $all_issues = array_merge($all_issues, $this->check_variations_selection_issues($attribute_map));

        // Group issues by attribute_id or variation_id
        foreach ($all_issues as $issue) {
            $attr_id = $issue['attribute_id'];
            $issue_data = [
                'type' => $issue['type'],
                'message' => $issue['message']
            ];
            if (!isset($issues_by_attribute[$attr_id])) {
                $issues_by_attribute[$attr_id]['name'] = $issue['name'];
                $issues_by_attribute[$attr_id]['issues'][] = $issue_data;
                // Mark variation issues differently
                if (strpos($attr_id, 'variation_') === 0) {
                    $issues_by_attribute[$attr_id]['is_variation'] = true;
                }
            } else {
                $issues_by_attribute[$attr_id]['issues'][] = $issue_data;
            }
        }
        return $issues_by_attribute;
    }

    /**
     * Build attribute map for dependency checking
     *
     * @param array $attributes The attributes array
     * @return array Mapped attributes with dependency information
     */
    private function build_attribute_map($attributes) {
        $attribute_map = [];
        foreach ($attributes as $key_a => $attribute) {
            foreach ($attribute['values'] as $key_v => $attribute_value) {
                $attribute_map[$attribute_value['products_attributes_id']] = [
                    'options_id' => $key_a,
                    'values_id' => $key_v,
                    'options_name' => $attribute['products_options_name'],
                    'values_name' => $attribute_value['products_options_values_name'],
                    'dependson_options_id' => $attribute_value['dependson_options_id'],
                    'dependson_options_values_id' => $attribute_value['dependson_options_values_id'],
                    'sort_order' => $attribute_value['products_attributes_sort_order']
                ];
            }
        }
        return $attribute_map;
    }


    /**
     * Get detailed explanation for a specific dependency issue type
     *
     * @param string $issue_type The type of issue
     * @return array Array containing title, description, and resolution steps
     */
    public function get_dependency_issue_explanation($issue_type) {
        $explanations = [
            'missing_dependency' => [
                'title' => '🔗 Missing Dependencies',
                'description' => 'This attribute depends on another attribute that doesn\'t exist in the product. When an attribute has a dependency, the dependent attribute must be present for the dependency to work correctly.',
                'resolution' => [
                    '1. Check the "Depends On" setting for this attribute',
                    '2. Either add the missing dependent attribute to the product',
                    '3. Or remove the dependency by setting "Depends On" to "None"',
                    '4. Verify that the dependent attribute has the correct option and value'
                ]
            ],
            'circular_dependency' => [
                'title' => '🔄 Circular Dependencies',
                'description' => 'Two or more attributes depend on each other in a loop, creating a circular dependency. This makes it impossible to determine the correct order of selection.',
                'resolution' => [
                    '1. Identify all attributes in the dependency loop',
                    '2. Break the loop by removing one of the dependencies',
                    '3. Reorganize dependencies to flow in one direction only',
                    '4. Consider if some dependencies are actually unnecessary'
                ]
            ],
            'sort_order_violation' => [
                'title' => '📊 Sort Order Violations',
                'description' => 'An attribute depends on another attribute that appears later in the sort order. Dependencies should always appear before the attributes that depend on them.',
                'resolution' => [
                    '1. Adjust the sort order so dependent attributes come first',
                    '2. The attribute being depended on should have a lower sort order number',
                    '3. Review all sort orders to ensure logical dependency flow',
                    '4. Test the attribute selection order in the frontend'
                ]
            ],
            'missing_variation' => [
                'title' => '🎯 Missing Variations',
                'description' => 'This attribute exists but has no corresponding product variations. Attributes should have at least one variation that uses them.',
                'resolution' => [
                    '1. Create variations that include this attribute',
                    '2. Or remove the attribute if it\'s no longer needed',
                    '3. Check if variations were accidentally deleted',
                    '4. Verify that the attribute is properly configured'
                ]
            ],
            'default_conflict' => [
                'title' => '⚠️ Default Conflicts',
                'description' => 'A default attribute depends on another attribute, but the current default for the dependency conflicts with this relationship
                
                This issue can occur when multiple attributes are set as default, and those defaults conflict with each other\'s dependencies.  
                
                Basically there could be a situation where selecting one default attribute will hide another due to dependencies.
                
                This is not BIG problem, as the system will fall back on the lowest in the sort order for that group.',

                'resolution' => [
                    '1. Review which attributes are set as default',
                    '2. Ensure default attributes don\'t conflict with dependencies',
                    '3. Adjust default settings to match dependency requirements',
                    '4. Consider removing default status from conflicting attributes'
                ]
            ],
            'self_option_group_dependency' => [
                'title' => '🚫 Self Option Group Dependencies',
                'description' => 'An attribute depends on another attribute from the same option group. This creates a logical impossibility since only one value from an option group can be selected at a time.',
                'resolution' => [
                    '1. Remove the dependency on the same option group',
                    '2. Create dependencies on attributes from different option groups',
                    '3. Review the logical flow of attribute selection',
                    '4. Consider splitting complex options into separate groups'
                ]
            ],
            'variation_missing_attribute' => [
                'title' => '❌ Missing Attributes',
                'description' => 'A product variation references attributes that don\'t exist in the product. This can happen when attributes are deleted but variations still reference them.',
                'resolution' => [
                    '1. Add the missing attributes back to the product',
                    '2. Or delete/update the variation to use existing attributes',
                    '3. Check for recently deleted attributes',
                    '4. Verify variation configuration is correct'
                ]
            ],
            'variation_duplicate_model' => [
                'title' => '📋 Duplicate Model Numbers',
                'description' => 'Multiple variations have the same model number. Each variation should have a unique model number for proper identification and inventory management.',
                'resolution' => [
                    '1. Review all variation model numbers',
                    '2. Assign unique model numbers to each variation',
                    '3. Follow your company\'s model numbering convention',
                    '4. Update any external systems that reference these models'
                ]
            ],
            'variation_missing_autodesk_link' => [
                'title' => '🔗 Missing Autodesk Links',
                'description' => 'This Autodesk product variation is missing a link to the Autodesk catalog. Autodesk products should be linked to their catalog entries for proper integration.',
                'resolution' => [
                    '1. Find the correct Autodesk catalog entry',
                    '2. Add the Autodesk catalog link to this variation',
                    '3. Verify the link is valid and active',
                    '4. Test the integration with Autodesk systems'
                ]
            ],
            'variation_invalid_autodesk_link' => [
                'title' => '❌ Invalid Autodesk Links',
                'description' => 'This variation has an Autodesk catalog link that doesn\'t exist or is invalid. This can break integration with Autodesk systems.',
                'resolution' => [
                    '1. Verify the Autodesk catalog link is correct',
                    '2. Update with a valid catalog entry',
                    '3. Remove the link if the product is no longer in the catalog',
                    '4. Contact Autodesk support if catalog issues persist'
                ]
            ],
            'variation_selection_issue' => [
                'title' => '⚡ Selection Issues',
                'description' => 'This variation is enabled but uses attributes that are disabled or have other issues. This can cause problems with product selection and ordering.',
                'resolution' => [
                    '1. Check if all attributes used by this variation are enabled',
                    '2. Disable the variation if its attributes are problematic',
                    '3. Fix the underlying attribute issues first',
                    '4. Re-enable the variation once attributes are resolved'
                ]
            ]
        ];

        return $explanations[$issue_type] ?? [
            'title' => '❓ Unknown Issue',
            'description' => 'An unknown issue type was encountered. This may indicate a system error or a new type of issue that needs to be addressed.',
            'resolution' => [
                '1. Contact system administrator',
                '2. Report this issue type: ' . $issue_type,
                '3. Provide details about when this occurred',
                '4. Check system logs for additional information'
            ]
        ];
    }


    /**
     * Check for missing dependencies
     *
     * @param array $attribute_map The attribute map
     * @return array Array of missing dependency issues
     */
    private function check_missing_dependencies($attribute_map) {
        $issues = [];
        foreach ($attribute_map as $attr_id => $attr) {
            if (tep_not_null($attr['dependson_options_id']) && tep_not_null($attr['dependson_options_values_id'])) {
                $found = false;
                foreach ($attribute_map as $check_attr) {
                    if ($check_attr['options_id'] == $attr['dependson_options_id'] &&
                        (strpos($attr['dependson_options_values_id'], $check_attr['values_id']) !== false)) {
                        $found = true;
                        break;
                    }
                }

                if (!$found) {
                    $issues[] = [
                        'type' => 'missing_dependency',
                        'name' => "{$attr['options_name']}: {$attr['values_name']}",
                        'attribute_id' => $attr_id,
                        'message' => "Attribute '{$attr['options_name']}: {$attr['values_name']}' depends on a missing attribute"
                    ];
                }
            }
        }
        return $issues;
    }

    /**
     * Check for sort order violations
     *
     * @param array $attribute_map The attribute map
     * @return array Array of sort order violation issues
     */
    private function check_sort_order_violations($attribute_map) {
        $issues = [];
        foreach ($attribute_map as $attr_id => $attr) {
            if (tep_not_null($attr['dependson_options_id']) && tep_not_null($attr['dependson_options_values_id'])) {
                foreach ($attribute_map as $check_attr) {
                    if ($check_attr['options_id'] == $attr['dependson_options_id'] &&
                        (strpos($attr['dependson_options_values_id'], $check_attr['values_id']) !== false)) {
                        if ($check_attr['sort_order'] > $attr['sort_order']) {
                            $issues[] = [
                                'type' => 'sort_order_violation',
                                'name' => "{$attr['options_name']}: {$attr['values_name']}",
                                'attribute_id' => $attr_id,
                                'message' => "Attribute '{$attr['options_name']}: {$attr['values_name']}' depends on '{$check_attr['options_name']}: {$check_attr['values_name']}' which has a higher sort order"
                            ];
                        }
                    }
                }
            }
        }
        return $issues;
    }

    /**
     * Check for circular dependencies
     *
     * @param array $attribute_map The attribute map
     * @return array Array of circular dependency issues
     */
    private function check_circular_dependencies($attribute_map) {
        $issues = [];

        // Build dependency graph
        $dependency_graph = [];
        foreach ($attribute_map as $attr_id => $attr) {
            if (tep_not_null($attr['dependson_options_id']) && tep_not_null($attr['dependson_options_values_id'])) {
                $dependency_graph[$attr_id] = [];
                foreach ($attribute_map as $dep_id => $dep_attr) {
                    if ($dep_attr['options_id'] == $attr['dependson_options_id'] &&
                        (strpos($attr['dependson_options_values_id'], $dep_attr['values_id']) !== false)) {
                        $dependency_graph[$attr_id][] = $dep_id;
                    }
                }
            }
        }

        // Detect cycles in the dependency graph
        foreach ($dependency_graph as $start_id => $deps) {
            $visited = [];
            $path = [$start_id];
            if ($this->has_cycle($dependency_graph, $start_id, $visited, $path)) {
                $cycle_path = [];
                foreach ($path as $attr_id) {
                    if (isset($attribute_map[$attr_id])) {
                        $cycle_path[] = "{$attribute_map[$attr_id]['options_name']}: {$attribute_map[$attr_id]['values_name']}";
                    }
                }
                $issues[] = [
                    'type' => 'circular_dependency',
                    'name' =>  $cycle_path[0],
                    'attribute_id' => $start_id,
                    'message' => "Circular dependency detected: " . implode(" > ", $cycle_path)
                ];
            }
        }

        return $issues;
    }

    /**
     * Helper method to detect cycles in a dependency graph
     *
     * @param array $graph The dependency graph
     * @param mixed $current Current node being checked
     * @param array $visited Reference to visited nodes array
     * @param array $path Reference to current path array
     * @return bool True if cycle is detected
     */
    private function has_cycle($graph, $current, &$visited, &$path) {
        $visited[$current] = true;

        if (isset($graph[$current])) {
            foreach ($graph[$current] as $neighbor) {
                if (!isset($visited[$neighbor])) {
                    $path[] = $neighbor;
                    if ($this->has_cycle($graph, $neighbor, $visited, $path)) {
                        return true;
                    }
                    array_pop($path);
                } else if (in_array($neighbor, $path)) {
                    $path[] = $neighbor;
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check for attributes that don't have corresponding variations
     *
     * @param array $attribute_map The attribute map
     * @return array Array of missing variation issues
     */
    private function check_missing_variations($attribute_map) {
        $issues = [];

        // Only check if product has variations
        if (!$this->attributes_instance->has_variations) {
            return $issues;
        }

        // Get all variations for this product
        $variations = $this->attributes_instance->get_variations();

        // Build a set of all attribute combinations that have variations
        $variation_combinations = [];
        foreach ($variations as $variation) {
            if (!empty($variation['attributes'])) {
                $variation_combinations[] = $variation['attributes'];
            }
        }

        // Check each attribute to see if it appears in at least one variation
        foreach ($attribute_map as $attr_id => $attr) {
            $found_in_variation = false;

            foreach ($variation_combinations as $combination) {
                if (isset($combination[$attr['options_id']]) &&
                    $combination[$attr['options_id']] == $attr['values_id']) {
                    $found_in_variation = true;
                    break;
                }
            }

            if (!$found_in_variation) {
                $issues[] = [
                    'type' => 'missing_variation',
                    'name' => "{$attr['options_name']}: {$attr['values_name']}",
                    'attribute_id' => $attr_id,
                    'message' => "Attribute '{$attr['options_name']}: {$attr['values_name']}' has no corresponding product variation"
                ];
            }
        }

        return $issues;
    }

    /**
     * Check whether the default attributes can all be displayed at once
     * (i.e., if selecting one default will hide another due to dependencies)
     *
     * @param array $attribute_map The attribute map
     * @return array Array of default conflict issues
     */
    private function check_default_conflicts($attribute_map) {
        $issues = [];

        // Get all default attributes
        $default_attributes = $this->attributes_instance->default_attributes;

        if (empty($default_attributes) || count($default_attributes) < 2) {
            return $issues; // No conflicts possible with 0 or 1 defaults
        }

        // Build a map of default attribute IDs for quick lookup
        $default_attr_ids = [];
        foreach ($attribute_map as $attr_id => $attr) {
            if (isset($default_attributes[$attr['options_id']]) &&
                $default_attributes[$attr['options_id']] == $attr['values_id']) {
                $default_attr_ids[$attr_id] = $attr;
            }
        }

        // Check each default attribute for dependencies that conflict with other defaults
        foreach ($default_attr_ids as $attr_id => $attr) {
            if (tep_not_null($attr['dependson_options_id']) && tep_not_null($attr['dependson_options_values_id'])) {
                // This default attribute has a dependency
                $dependency_satisfied = false;

                // Check if the dependency is satisfied by another default attribute
                foreach ($default_attr_ids as $check_attr_id => $check_attr) {
                    if ($check_attr['options_id'] == $attr['dependson_options_id'] &&
                        (strpos($attr['dependson_options_values_id'], $check_attr['values_id']) !== false)) {
                        $dependency_satisfied = true;
                        break;
                    }
                }

                if (!$dependency_satisfied) {
                    // Find what the dependency actually requires
                    $required_values = explode(',', $attr['dependson_options_values_id']);
                    $required_names = [];

                    foreach ($required_values as $required_value_id) {
                        $required_value_id = trim($required_value_id);
                        foreach ($attribute_map as $map_attr) {
                            if ($map_attr['options_id'] == $attr['dependson_options_id'] &&
                                $map_attr['values_id'] == $required_value_id) {
                                $required_names[] = $map_attr['values_name'];
                                break;
                            }
                        }
                    }

                    $required_options_name = '';
                    foreach ($attribute_map as $map_attr) {
                        if ($map_attr['options_id'] == $attr['dependson_options_id']) {
                            $required_options_name = $map_attr['options_name'];
                            break;
                        }
                    }

                    $issues[] = [
                        'type' => 'default_conflict',
                        'name' => "{$attr['options_name']}: {$attr['values_name']}",
                        'attribute_id' => $attr_id,
                        'message' => "Default attribute '{$attr['options_name']}: {$attr['values_name']}' depends on '{$required_options_name}: " . implode(' or ', $required_names) . "' but the current default for that option conflicts with this dependency"
                    ];
                }
            }
        }

        return $issues;
    }
    function tcs_draw_attributes_dependency_warnings():string {
        // Add dependency check warnings if product ID exists
        $dependency_warnings = '';
        $issues = $this->check_dependency_issues();
        if (!empty($issues)) {
            // Include CSS for dependency issues if not already included
            $dependency_warnings = '<link rel="stylesheet" href="includes/css/dependency-issues.css">';

            // Add JavaScript for modal functionality
            $dependency_warnings .= '<script>
        function openModal(modalId) {
            document.getElementById(modalId).style.display = "flex";
        }
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = "none";
        }
        </script>';

            $dependency_warnings .= '<div class="alert alert-warning">';
            $dependency_warnings .= '<button type="button" class="close" x-on:click="show = false" aria-label="Close">';
            $dependency_warnings .= '<span aria-hidden="true">&times;</span>';
            $dependency_warnings .= '</button>';
            $dependency_warnings .= '<h4>Attribute Dependency Issues:</h4>';
            $dependency_warnings .= '<ul>';

            $modal_counter = 0;
            // Iterate through issues indexed by attribute_id
            foreach ($issues as $attribute_id => $issue_data) {
                // Check if this attribute has multiple issues (array format) or single issue
                $ii = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" style="color: #8a6d3b" class="bi bi-info-circle" viewBox="0 0 16 16">
  <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16"/>
  <path d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0"/>
</svg>';
                if (isset($issue_data['issues'])) {
                    $dependency_warnings .= "<li><strong>Attribute {$attribute_id} {$issue_data['name']}:</strong><ul>";
                    // Multiple issues for this attribute
                    foreach ($issue_data['issues'] as $index => $single_issue) {
                        $modal_id = "issue_modal_" . $modal_counter++;
                        $issue_type = $single_issue['type'] ?? 'unknown';
                        $explanation = $this->get_dependency_issue_explanation($issue_type);

                        $dependency_warnings .= "<li class='issue-type-{$issue_type}'><strong>{$index}: </strong>{$single_issue['message']}";
                        $dependency_warnings .= " <a  class='' ";
                        $dependency_warnings .= "onclick='openModal(\"{$modal_id}\")' ";
                        $dependency_warnings .= "title='Click for detailed explanation and resolution steps'><strong> $ii</strong></a>";
                        $dependency_warnings .= "</li>";

                        // Add modal for this issue
                        $dependency_warnings .= $this->tcs_draw_issue_info_modal($modal_id, $explanation);
                    }
                    $dependency_warnings .= '</ul></li>';
                } else {
                    // Single issue for this attribute
                    $modal_id = "issue_modal_" . $modal_counter++;
                    $issue_type = $issue_data['type'] ?? 'unknown';
                    $explanation = $this->get_dependency_issue_explanation($issue_type);

                    $dependency_warnings .= "<li class='issue-type-{$issue_type}'><strong>Attribute ID {$attribute_id}:</strong> {$issue_data['message']}";
                    $dependency_warnings .= "<a  class='' ";
                    $dependency_warnings .= "onclick='openModal(\"{$modal_id}\")' ";
                    $dependency_warnings .= "title='Click for detailed explanation and resolution steps'><strong> $ii</strong></a>";
                    $dependency_warnings .= "</li>";

                    // Add modal for this issue
                    $dependency_warnings .= $this->tcs_draw_issue_info_modal($modal_id, $explanation);
                }
            }

            $dependency_warnings .= '</ul>';
            $dependency_warnings .= '</div>';
        }
        return $dependency_warnings;
    }

    /**
     * Generate modal HTML for displaying issue information
     *
     * @param string $modal_id Unique ID for the modal
     * @param array $explanation Explanation data from get_dependency_issue_explanation
     * @return string Modal HTML
     */
    function tcs_draw_issue_info_modal($modal_id, $explanation) {
        $title = htmlspecialchars($explanation['title']);
        $description = htmlspecialchars($explanation['description']);
        $resolution_steps = '';

        foreach ($explanation['resolution'] as $step) {
            $resolution_steps .= '<div style="margin-bottom: 8px;">' . htmlspecialchars($step) . '</div>';
        }

        return '
    <div id="' . $modal_id . '"
         onclick="closeModal(\'' . $modal_id . '\')"
         style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1050; align-items: center; justify-content: center;">

        <div onclick="event.stopPropagation()"
             style="background: white; border-radius: 8px; max-width: 600px; width: 90%; margin: 20px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);">

            <!-- Modal Header -->
            <div style="padding: 15px 20px; border-bottom: 1px solid #dee2e6; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); display: flex; align-items: center; justify-content: space-between;">
                <h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #2c3e50;">
                    ' . $title . '
                </h3>
                <button onclick="closeModal(\'' . $modal_id . '\')"
                        style="background: none; border: none; font-size: 24px; cursor: pointer; padding: 5px; color: #999;">
                    <span>&times;</span>
                </button>
            </div>

            <!-- Modal Body -->
            <div style="padding: 20px;">
                <div style="margin-bottom: 15px;">
                    <h4 style="color: #34495e; font-weight: 600; margin-bottom: 8px; font-size: 14px;">What this means:</h4>
                    <p style="line-height: 1.6; color: #555; margin-bottom: 15px;">' . $description . '</p>
                </div>

                <div>
                    <h4 style="color: #34495e; font-weight: 600; margin-bottom: 8px; font-size: 14px;">How to resolve:</h4>
                    <div style="line-height: 1.6; color: #555;">
                        ' . $resolution_steps . '
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div style="padding: 15px 20px; border-top: 1px solid #dee2e6; text-align: right;">
                <button onclick="closeModal(\'' . $modal_id . '\')"
                        class="btn btn-secondary"
                        style="background-color: #6c757d; border-color: #6c757d; color: white; padding: 6px 12px; border-radius: 4px; border: none; cursor: pointer;">
                    Close
                </button>
            </div>
        </div>
    </div>';
    }
    /**
     * Check for attributes that depend on items from their own option group
     *
     * @param array $attribute_map The attribute map
     * @return array Array of self option group dependency issues
     */
    private function check_self_option_group_dependencies($attribute_map) {
        $issues = [];

        foreach ($attribute_map as $attr_id => $attr) {
            // Check if this attribute has a dependency
            if (tep_not_null($attr['dependson_options_id']) && tep_not_null($attr['dependson_options_values_id'])) {
                // Check if the dependency is on the same option group as the attribute itself
                if ($attr['dependson_options_id'] == $attr['options_id']) {
                    $issues[] = [
                        'type' => 'self_option_group_dependency',
                        'name' => "{$attr['options_name']}: {$attr['values_name']}",
                        'attribute_id' => $attr_id,
                        'message' => "Attribute '{$attr['options_name']}: {$attr['values_name']}' depends on another item from its own option group '{$attr['options_name']}'. Items cannot depend on other items from the same option group."
                    ];
                }
            }
        }

        return $issues;
    }

    /**
     * Check for variations that reference missing attributes
     *
     * @param array $attribute_map The attribute map
     * @return array Array of missing attribute issues in variations
     */
    private function check_variations_missing_attributes($attribute_map) {
        $issues = [];

        // Only check if product has variations
        if (!$this->attributes_instance->has_variations) {
            return $issues;
        }

        // Get all variations for this product
        $variations = $this->attributes_instance->get_variations();

        foreach ($variations as $variation) {
            if (!empty($variation['attributes'])) {
                foreach ($variation['attributes'] as $option_id => $value_id) {
                    // Check if this attribute combination exists in the attributes table
                    $found = false;
                    foreach ($attribute_map as $attr_id => $attr) {
                        if ($attr['options_id'] == $option_id && $attr['values_id'] == $value_id) {
                            $found = true;
                            break;
                        }
                    }

                    if (!$found) {
                        // Try to get option and value names from database
                        $option_name = $this->attributes_instance->options_name($option_id) ?: "Option ID: $option_id";
                        $value_name = $this->attributes_instance->values_name($value_id) ?: "Value ID: $value_id";

                        $issues[] = [
                            'type' => 'variation_missing_attribute',
                            'name' => "Variation: {$variation['model']}",
                            'attribute_id' => 'variation_' . $variation['products_variations_id'],
                            'message' => "Variation '{$variation['model']}' references missing attribute '{$option_name}: {$value_name}'"
                        ];
                    }
                }
            }
        }

        return $issues;
    }

    /**
     * Check for duplicate model numbers in variations
     *
     * @return array Array of duplicate model issues
     */
    private function check_variations_duplicate_models() {
        $issues = [];

        // Only check if product has variations
        if (!$this->attributes_instance->has_variations) {
            return $issues;
        }

        // Get all variations for this product
        $variations = $this->attributes_instance->get_variations();
        $model_counts = [];
        $model_variations = [];

        // Count occurrences of each model
        foreach ($variations as $variation) {
            $model = trim($variation['model']);
            if (!empty($model)) {
                if (!isset($model_counts[$model])) {
                    $model_counts[$model] = 0;
                    $model_variations[$model] = [];
                }
                $model_counts[$model]++;
                $model_variations[$model][] = $variation;
            }
        }

        // Find duplicates
        foreach ($model_counts as $model => $count) {
            if ($count > 1) {
                foreach ($model_variations[$model] as $variation) {
                    $issues[] = [
                        'type' => 'variation_duplicate_model',
                        'name' => "Variation: {$variation['model']}",
                        'attribute_id' => 'variation_' . $variation['products_variations_id'],
                        'message' => "Variation '{$variation['model']}' has a duplicate model number (appears {$count} times)"
                    ];
                }
            }
        }

        return $issues;
    }

    /**
     * Check for Autodesk link issues in variations
     *
     * @return array Array of Autodesk link issues
     */
    private function check_variations_autodesk_links() {
        $issues = [];

        // Only check if product has variations
        if (!$this->attributes_instance->has_variations) {
            return $issues;
        }

        // Get all variations for this product
        $variations = $this->attributes_instance->get_variations();

        foreach ($variations as $variation) {
            // Check if this is an Autodesk product (manufacturers_id = 15)
            if ($variation['manufacturers_id'] == 15) {
                $autodesk_hash = trim($variation['autodesk_catalog_unique_hash']);

                if (empty($autodesk_hash) || $autodesk_hash === 'NULL') {
                    $issues[] = [
                        'type' => 'variation_missing_autodesk_link',
                        'name' => "Variation: {$variation['model']}",
                        'attribute_id' => 'variation_' . $variation['products_variations_id'],
                        'message' => "Autodesk variation '{$variation['model']}' is missing Autodesk catalog link"
                    ];
                } else {
                    // Check if the Autodesk link is valid (exists in catalog)
                    $catalog_check = tep_db_query("SELECT id FROM products_autodesk_catalog WHERE unique_hash = '" . tep_db_input($autodesk_hash) . "' LIMIT 1");
                    if (tep_db_num_rows($catalog_check) == 0) {
                        $issues[] = [
                            'type' => 'variation_invalid_autodesk_link',
                            'name' => "Variation: {$variation['model']}",
                            'attribute_id' => 'variation_' . $variation['products_variations_id'],
                            'message' => "Autodesk variation '{$variation['model']}' has invalid Autodesk catalog link: {$autodesk_hash}"
                        ];
                    }
                }
            }
        }

        return $issues;
    }

    /**
     * Check for variations selection issues (enabled variations with disabled attributes)
     *
     * @param array $attribute_map The attribute map
     * @return array Array of variation selection issues
     */
    private function check_variations_selection_issues($attribute_map) {
        $issues = [];

        // Only check if product has variations
        if (!$this->attributes_instance->has_variations) {
            return $issues;
        }

        // Get all variations for this product
        $variations = $this->attributes_instance->get_variations();

        foreach ($variations as $variation) {
            // Check if variation is enabled but has disabled attributes
            if ($variation['enabled'] == 1 && !empty($variation['attributes'])) {
                $disabled_attributes = [];

                foreach ($variation['attributes'] as $option_id => $value_id) {
                    // Check if this attribute is enabled in the attributes table
                    $attribute_enabled = false;
                    foreach ($attribute_map as $attr_id => $attr) {
                        if ($attr['options_id'] == $option_id && $attr['values_id'] == $value_id) {
                            // Check if attribute is enabled (you may need to adjust this based on your enabled field logic)
                            $attribute_enabled = true;
                            break;
                        }
                    }

                    if (!$attribute_enabled) {
                        $option_name = $this->attributes_instance->options_name($option_id) ?: "Option ID: $option_id";
                        $value_name = $this->attributes_instance->values_name($value_id) ?: "Value ID: $value_id";
                        $disabled_attributes[] = "{$option_name}: {$value_name}";
                    }
                }

                if (!empty($disabled_attributes)) {
                    $issues[] = [
                        'type' => 'variation_selection_issue',
                        'name' => "Variation: {$variation['model']}",
                        'attribute_id' => 'variation_' . $variation['products_variations_id'],
                        'message' => "Enabled variation '{$variation['model']}' contains disabled attributes: " . implode(', ', $disabled_attributes)
                    ];
                }
            }
        }

        return $issues;
    }
}
