<?php
namespace api\subscriptions;

use autodesk_api\AutodeskAPI;
use autodesk_api\AutodeskSubscriptions;
use Edge\Edge;
use data_table\data_table;
use DataImporter;
use DateTime;

//print_rr($input_params);


function import_csv_into_database(){
    AutodeskAPI::import_csv_into_database(AutodeskSubscriptions::get_subscription_column_mapping(), DIR_FS_CATALOG . "feeds/subscriptions.csv");
}

function search($p){
    return generate_subscription_table(criteria: ["search" => $p['search_terms'], "limit" => 50], just_body: false);
}


function data_table_filter($p){
    $criteria = data_table::api_process_criteria($p);
    return generate_subscription_table(criteria: $criteria);
}

function create_quote($p){
    $autodesk = new AutodeskAPI();
    $quote = $autodesk->quote->create_renewal($p['subs_subscriptionId'], $p['user_id']);
    print_rr($quote,'quoty1');
    $quote_assoc = json_decode($quote, true);
    print_rr($quote_assoc,'quoty2');
    return edge::render('quote-v3-ui-form', ['quote_data' => $quote_assoc]);
}

function send_quote($p){
    $autodesk = new AutodeskAPI();
    $quotedata = $p['quote'];
    $quote = $autodesk->quote->send_to_autodesk($quotedata);
    print_rr(i:['quote' => $quote,'p' => $p],co:false,full:true);
    return edge::render('layout-card', ['content' =>  $quote]);
}

function emails_send_reminders(){
    $autodesk = new AutodeskAPI();
    $subs = $autodesk->subscriptions->get_renewable();
    $file_path = FS_APP_ROOT . 'resources/views/subscriptions/email_history/reminder_email.view.php';
    $email_template = file_get_contents($file_path);
    if ($email_template === false) {
        throw new Exception("Failed to load email template from {$file_path}");
    }

    // Split the content by lines
    $lines = explode("\n", $email_template);

    $email_rules = explode(',', AutodeskAPI::database_get_storage('subscription_renew_email_send_rules'));
    $settings_days = AutodeskAPI::database_get_storage('subscription_renew_email_send_days');
    $settings_time = AutodeskAPI::database_get_storage('subscription_renew_email_send_time');


    // Check if it's time to send an email.
    foreach ($subs as $sub) {
        if ($subs['tcs_unsubscribe'] = 1) continue;
        $now = new DateTime();
        $end_date = new DateTime($sub['endDate']);
        $date_last_sent = $sub['last_email_sent_date'] ?? $sub['startDate'];
        $last_sent = new DateTime($date_last_sent);
        $days_remaining = $now->diff($end_date)->days;
        $days_since_last_sent = $now->diff($last_sent)->days;
        echo "Processing subscription " . $sub['subscriptionReferenceNumber'] . " id " . $sub['id'] . " for customer: " . $sub['endCustomer_name'] . " End date is " . $sub['endDate'] . " with d/r: {$days_remaining} and last sent: " . $sub['last_email_sent_date'] . ": ";
        foreach ($email_rules as $key => $rule) {
            if ($days_remaining <= $rule) {
                echo " is below rule $rule.<br> " . PHP_EOL;
                if ((($days_remaining + $days_since_last_sent) > $rule)) {
                    echo "sending email <br>" . PHP_EOL;
                    $autodesk->subscriptions->send_reminder_email($sub['id']);
                    //print_rr($autodesk->subscriptions->send_reminder_email($sub['id'], $rule));
                }
                break;
            }
        }
    }
}

function email_send_reminder(){
    $autodesk = new AutodeskAPI();
    $subs = $autodesk->subscriptions->get_renewable();
    $file_path = FS_APP_ROOT . 'resources/views/subscriptions/email_history/reminder_email.view.php';
    $email_template = file_get_contents($file_path);
    if ($email_template === false) {
        throw new Exception("Failed to load email template from {$file_path}");
    }

    // Split the content by lines
    $lines = explode("\n", $email_template);

    $email_rules = explode(',', AutodeskAPI::database_get_storage('subscription_renew_email_send_rules'));
    $settings_days = AutodeskAPI::database_get_storage('subscription_renew_email_send_days');
    $settings_time = AutodeskAPI::database_get_storage('subscription_renew_email_send_time');


    // Check if it's time to send an email.
    foreach ($subs as $sub) {
        if ($subs['tcs_unsubscribe'] = 1) continue;
        $now = new DateTime();
        $end_date = new DateTime($sub['endDate']);
        $date_last_sent = $sub['last_email_sent_date'] ?? $sub['startDate'];
        $last_sent = new DateTime($date_last_sent);
        $days_remaining = $now->diff($end_date)->days;
        $days_since_last_sent = $now->diff($last_sent)->days;
        echo "Processing subscription " . $sub['subscriptionReferenceNumber'] . " id " . $sub['id'] . " for customer: " . $sub['endCustomer_name'] . " End date is " . $sub['endDate'] . " with d/r: {$days_remaining} and last sent: " . $sub['last_email_sent_date'] . ": ";
        foreach ($email_rules as $key => $rule) {
            if ($days_remaining <= $rule) {
                echo " is below rule $rule.<br> " . PHP_EOL;
                if ((($days_remaining + $days_since_last_sent) > $rule)) {
                    echo "sending email <br>" . PHP_EOL;
                    $autodesk->subscriptions->send_reminder_email($sub['id']);
                    //print_rr($autodesk->subscriptions->send_reminder_email($sub['id'], $rule));
                }
                break;
            }
        }
    }
}

function flag_modal($p):string {
    return Edge::render('component-add-flag', $p);
}

function add_flag($p):void {
    $data = [
        'id' => $p['sub_id'],
        'subscriptionReferenceNumber' => $p['sub_num'],
        'media' => $p['label'],
        'user_id' => $p['user_id'],
        'message' => $p['message']
    ];
    $autodesk = new AutodeskAPI();
    $history = $autodesk->subscription->insert_history_item($data);
}


function view($p){

    $autodesk = new AutodeskAPI();
    $where = $where_hist = [];

    if (isset($p['subscription_number'])) {
        $where['subs.subscriptionReferenceNumber'] = ['=', $p['subscription_number']];
        $where_hist['hist.subscription_reference_number'] = ['=', $p['subscription_number']];
    } elseif (isset($p['subscription_id'])) {
        $where['subscriptionId'] = ['=', $p['subscription_id']];
    } else {
        print_rr("No subscription number or id provided");
    }

    $sub_display = [
        'Subscription' => [
            'label' => 'Subscription',
            'collapsed' => 'false',
            'content' => [
                'subs_subscriptionId' => ['label' => 'subscriptionId', 'value' => ''],
                'subs_subscriptionReferenceNumber' => ['label' => 'subscriptionReferenceNumber', 'value' => ''],
                'subs_status' => ['label' => 'status', 'value' => ''],
                'subs_startDate' => ['label' => 'startDate', 'value' => ''],
                'subs_endDate' => ['label' => 'endDate', 'value' => ''],
                'subs_term' => ['label' => 'term', 'value' => '']

            ],
        ],
        'endcust' => [
            'label' => "End Customer",
            'collapsed' => 'false',
            'class_suffix' => '',
            'internal_class_suffix' => 'flex flex-wrap',
            'content' => [
                'endcustomerdeets' => [
                    'label' => '',
                    'type' => 'html-div',
                    'class_suffix' => 'flex-grow',
                    'content' => [
                        'endcust_account_csn' => ['label' => 'csn', 'value' => ''],
                        'endcust_name' => ['label' => 'name', 'value' => ''],
                        'endcust_account_type' => ['label' => 'type', 'value' => ''],

                    ]
                ],
                'primaryAdminName' => [
                    'label' => 'Primary Admin',
                    'type' => 'layout-box',
                    'class_suffix' => '',
                    'hide_labels' => true,
                    'content' => [
                        'endcust_primary_admin_first_name' => ['label' => 'Name', 'value' => ''],
                        'endcust_primary_admin_last_name' => ['label' => '', 'value' => ''],
                        'endcust_primary_admin_email' => ['label' => 'email']
                    ]
                ],
                'address' => [
                    'label' => 'Address',
                    'type' => 'layout-box',
                    'class_suffix' => '',
                    'hide_labels' => true,
                    'content' => [
                        'endcust_address1' => ['label' => '', 'value' => ''],
                        'endcust_address2' => ['label' => '', 'value' => ''],
                        'endcust_address3' => ['label' => '', 'value' => ''],
                        'endcust_city' => ['label' => '', 'value' => ''],
                        'endcust_state_province' => ['label' => '', 'value' => ''],
                        'endcust_postal_code' => ['label' => '', 'value' => ''],
                        'endcust_country' => []
                    ]
                ],
            ]
        ],
        'timeline' => [
            'label' => 'Timeline',
            'collapsed' => 'false',
            'type' => 'layout-card',
            'class_suffix' => 'row-span-4',
            'content' => [
                'timeline' => [
                        'label' => 'Timeline',
                        'collapsed' => 'false',
                        'type' => 'component-activity-feed',
                        'content' => 'histdata'
                 ]
            ]
        ],
        'product' => [
            'label' => 'Product',
            'class_suffix' => 'col-span-2',
            'collapsed' => 'false',
            'internal_class_suffix' => 'grid grid-cols-4',
            'content' => [
                'subs_offeringId' => ['label' => 'offeringId', 'value' => ''],
                'subs_offeringCode' => ['label' => 'offeringCode', 'value' => ''],
                'subs_offeringName' => ['label' => 'Name'],
                'subs_quantity' => ['label' => 'quantity', 'value' => ''],
            ]
        ],
        "extra" => [
            'label' => 'More Info',
            'collapsed' => 'true',
            'class_suffix' => 'col-span-2 ',
            'content' => [
                'subs' => [
                    'label' => 'More subscription Info',
                    'collapsed' => 'true',
                    'content' => [
                        'subs_billingBehavior' => ['label' => 'billingBehavior', 'value' => ''],
                        'subs_billingFrequency' => ['label' => 'billingFrequency', 'value' => ''],
                        'subs_intendedUsage' => ['label' => 'intendedUsage', 'value' => ''],
                        'subs_connectivity' => ['label' => 'connectivity', 'value' => ''],
                        'subs_connectivityInterval' => ['label' => 'connectivityInterval', 'value' => ''],
                        'subs_opportunityNumber' => ['label' => 'opportunityNumber', 'value' => ''],
                        'subs_servicePlan' => ['label' => 'servicePlan', 'value' => ''],
                        'subs_accessModel' => ['label' => 'accessModel', 'value' => ''],
                        'subs_paymentMethod' => ['label' => 'paymentMethod', 'value' => ''],
                        'subs_recordType' => ['label' => 'recordType', 'value' => ''],
                        'subs_renewalCounter' => ['label' => 'renewalCounter', 'value' => ''],
                        'subs_autoRenew' => ['label' => 'autoRenew', 'value' => ''],
                    ]
                ],
                'endcust' => [
                    'label' => 'More Customer Info',
                    'collapsed' => 'true',
                    'class_suffix' => 'basis-full',
                    'content' => [
                        'endcust_individual_flag' => ['label' => 'individualFlag', 'value' => ''],
                        'endcust_named_account_flag' => ['label' => 'namedAccountFlag', 'value' => ''],
                        'endcust_named_account_group' => ['label' => 'namedAccountGroup', 'value' => ''],
                        'endcust_parent_industry_group' => ['label' => 'parentIndustryGroup', 'value' => ''],
                        'endcust_parent_industry_segment' => ['label' => 'parentIndustrySegment', 'value' => ''],
                        'endcust_team_id' => ['label' => 'teamId', 'value' => ''],
                        'endcust_team_name' => ['label' => 'teamName']
                    ]
                ],
                "soldto" => [
                    'label' => 'Sold To',
                    'collapsed' => 'true',
                    'content' => [
                        'soldto_account_csn' => ['label' => 'csn', 'value' => ''],
                        'soldto_name' => ['label' => 'name']
                    ]
                ],
                'solpro' => [
                    'label' => "Solution Provider",
                    'collapsed' => 'true',
                    'content' => [
                        'solpro_account_csn' => ['label' => 'csn', 'value' => ''],
                        'solpro_name' => ['label' => 'name', 'value' => ''],
                        'solpro_local_language_name' => ['label' => 'localLanguageName', 'value' => ''],
                        'solpro_account_type' => ['label' => 'type', 'value' => ''],
                        'solpro_address1' => ['label' => 'address1', 'value' => ''],
                        'solpro_address2' => ['label' => 'address2', 'value' => ''],
                        'solpro_address3' => ['label' => 'address3', 'value' => ''],
                        'solpro_city' => ['label' => 'city', 'value' => ''],
                        'solpro_state_province' => ['label' => 'stateProvince', 'value' => ''],
                        'solpro_postal_code' => ['label' => 'postalCode', 'value' => ''],
                        'solpro_country' => ['label' => 'country', 'value' => ''],
                        'solpro_state_province_code' => ['label' => 'state_province_code', 'value' => ''],
                        'solpro_country_code' => ['label' => 'countryCode', 'value' => ''],
                    ]
                ],
                'resell' => [
                    'label' => "Nurture Reseller",
                    'collapsed' => 'true',
                    'content' => [
                        'resell_account_csn' => ['label' => 'csn', 'value' => ''],
                        'resell_name' => ['label' => 'name', 'value' => ''],
                        'resell_lockDate' => ['label' => 'lockDate', 'value' => ''],
                        'resell_nurture_discount_eligibility' => ['label' => 'nurture_discount_eligibility', 'value' => ''],
                    ]
                ],
            ]
        ]
    ];

    $hist_columns = [
        'hist_id',
        'hist_subscription_id',
        'hist_subscription_reference_number',
        'hist_date',
        'hist_media',
        'hist_message',
        'users_name'
];
    $columns = parse_tables($sub_display);
    print_rr($columns, 'columns');
    $subscription = $autodesk->subscription->get($columns, ['where' => $where]);

    print_rr($subscription, 'subscription');
    $sub_display = parse_layout($sub_display, $subscription);
    $history = $autodesk->subscription->gethistory($hist_columns, ['where' => $where_hist]);
    $out = '<!-- Modal -->';
    print_rr($sub_display, 'sub display');
    $out .= Edge::render('view-subscription_display', ['data' => $sub_display, 'histdata' => $history]);
    echo $out;
}

function parse_tables($schema, $fields = []) {
    foreach ($schema as $key => $value) {
        if (is_array($value)) {
            if (isset($value['content'])) {
                $fields = parse_tables($value['content'], $fields);
            } elseif (isset($value['value'])) {
                $fields[$key] = $key;
            }
        }
    }
    return $fields;
}

function parse_layout($layout, $data) {
    foreach ($layout as $key => $value) {
        if (is_array($value)) {
            if (isset($value['content'])) {
                $layout[$key]['content'] = parse_layout($value['content'], $data);
            } elseif (isset($value['value'])) {
                $layout[$key]['value'] = $data[$key] ?? '';
            }
        }
    }
    return $layout;
}
