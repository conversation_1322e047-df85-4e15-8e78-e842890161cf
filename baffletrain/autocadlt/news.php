<?php
/*
  $Id: stats_products_viewed.php,v 1.29 2003/06/29 22:50:52 hpdl Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

  require('includes/application_top.php');
  function tep_get_news_name($name_id, $language_id = 0) {
    global $languages_id;

    if ($language_id == 0) $language_id = $languages_id;
    $name_query = tep_db_query("select name from " . TABLE_NEWS_DESC . " where news_id = '" . (int)$name_id . "' and language_id = '" . (int)$language_id . "'");
    $name = tep_db_fetch_array($name_query);

    return $name['name'];
  }

  function tep_get_news_content($news_id, $language_id) {
    $news_query = tep_db_query("select content from " . TABLE_NEWS_DESC . " where news_id = '" . (int)$news_id . "' and language_id = '" . (int)$language_id . "'");
    $news = tep_db_fetch_array($news_query);

    return $news['content'];
  }

  function tep_reply($news_id) {
    $reply_query = tep_db_query("select approved from " . TABLE_NEWS_REPLYS . " where news_id = '" . (int)$news_id . "'");
    while ($reply = tep_db_fetch_array($reply_query)) {
    $reply_test .= $reply['approved'];
    }
   if (preg_match("/0/", $reply_test)) {
    return true;
   } else {
   return false;
   }
  }
  
// create news article
  if ($_POST['new_news'] == 'new') {
     $date = date("Y-m-d G:i:s");
     $month_date = date("F Y");
     $sql_data_array = array( 'month_date' => $month_date,
                              'date_created' => $date);
      tep_db_perform(TABLE_NEWS, $sql_data_array);
    $news_id = tep_db_insert_id();

   $languages = tep_get_languages();
          for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
            $language_id = $languages[$i]['id'];

            $sql_data_array = array('news_id' => $news_id,
                                       'language_id' => $language_id,
                                       'name' => tep_db_prepare_input($_POST['news_name'][$language_id]),
                                    'content' => tep_db_prepare_input($_POST['news_content'][$language_id]));

              tep_db_perform(TABLE_NEWS_DESC, $sql_data_array);
          }

   tep_redirect(tep_href_link('news.php'));
    }
  
// edit news article
  if ($_POST['edit_news'] == 'edit') {
   $id = (int)tep_db_prepare_input($_POST['id']);

    $languages = tep_get_languages();
          for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
            $language_id = $languages[$i]['id'];

            $sql_data_array = array('name' => tep_db_prepare_input($_POST['news_name'][$language_id]),
                                    'content' => tep_db_prepare_input($_POST['news_content'][$language_id]));

      tep_db_perform(TABLE_NEWS_DESC, $sql_data_array, 'update', "news_id = '" . (int)$id . "' and language_id = '" . (int)$language_id . "'");
        }

   tep_redirect(tep_href_link('news.php'));
  }
// edit reply
    if ($_GET['action'] == 'edit_reply') {
   $id = (int)tep_db_prepare_input($_POST['id']);
   $name = tep_db_prepare_input($_POST['name']);
   $content = tep_db_prepare_input($_POST['content']);
   $username = tep_db_prepare_input($_POST['username']);
   $approved = tep_db_prepare_input($_POST['approved']);
  if ((empty($name)) || (empty($content))) {
         $error = 'You must enter a value into the name and content.';
      } else {
     $sql_data_array = array('name' => $name,
                              'username' => $username,
                              'content' => $content,
                              'approved' => $approved);
      tep_db_perform(TABLE_NEWS_REPLYS, $sql_data_array, 'update', "id = '" . (int)$id . "'");
   tep_redirect(tep_href_link('news.php', 'action=edit_reply&id=' . $_GET['id']));
    }
  }
// delete news article
  if ($_POST['delete'] == '1') {
     $id = (int)tep_db_prepare_input($_POST['id']);
   tep_db_query("delete from " . TABLE_NEWS . " where id= $id");
   tep_db_query("delete from " . TABLE_NEWS_DESC . " where news_id= '$id'");
   tep_db_query("delete from " . TABLE_NEWS_REPLYS . " where news_id= $id");
  tep_redirect(tep_href_link('news.php'));
  }
// delete reply
  if ($_GET['action'] == 'delete_reply') {
     $id = (int)tep_db_prepare_input($_POST['id']);
   tep_db_query("delete from " . TABLE_NEWS_REPLYS . " where id= $id");
  tep_db_query('update ' . TABLE_NEWS . ' set replys = replys-1 where id = ' . (int)$_GET['id']);
  tep_redirect(tep_href_link('news.php', 'action=edit&id=' . $_GET['id']));
  }

  $languages = tep_get_languages();

  require('includes/languages/' . $language . '/news.php');


  require('includes/template_top.php');
?><table border="0" width="100%" cellspacing="0" cellpadding="0">
      <tr>
        <td colspan="2"><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading"><?php echo HEADING_TITLE; ?></td>
            <td class="pageHeading" align="right">
           <form action="<?php echo tep_href_link('news.php'); ?>" method="get">
           <select name="month">
<?php
    $date_query = tep_db_query("select distinct month_date from " . TABLE_NEWS . " order by month_date asc");
     while ($date = tep_db_fetch_array($date_query)) {
?>
           <option value="<?php echo $date['month_date'] . '">' . $date['month_date']; ?></option>
<?php
 }
?>
           </select>
           <input type="submit" value="Go">
           </form></td>
          </tr>
        </table></td>
      </tr>
      <tr>
<?php
      if ($_GET['action'] == 'new') {
?>
       <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
       <form action="<?php echo tep_href_link('news.php', 'action=new'); ?>" method="post">
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
<?php
    for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
?>
          <tr>
            <td class="main"><?php if ($i == 0) echo HEADING_NAME; ?></td>
            <td class="main"><?php echo tep_image(DIR_WS_CATALOG_LANGUAGES . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('news_name[' . $languages[$i]['id'] . ']'); ?></td>
          </tr>
<?php
    }
?>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
<?php
    for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
?>
          <tr>
            <td class="main" valign="top"><?php if ($i == 0) echo HEADING_CONTENT; ?></td>
            <td><table border="0" cellspacing="0" cellpadding="0">
              <tr>
                <td class="main" valign="top"><?php echo tep_image(DIR_WS_CATALOG_LANGUAGES . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], $languages[$i]['name']); ?>&nbsp;</td>
                <td class="main"><?php echo tep_draw_textarea_field_ckeditor('news_content[' . $languages[$i]['id'] . ']', 'soft', '70', '15'); ?></td>
              </tr>
            </table></td>
          </tr>
<?php
    }
?>
            <tr>
            <td align="right"><input type="submit" value="Submit"><input type="hidden" name= "new_news" value="new"></td>
            <td>&nbsp;</td>
            </tr></form>
<?php
  } elseif ($_GET['action'] == 'edit') {
   $id = tep_db_prepare_input($_GET['id']);
  //$news_query_raw = tep_db_query("select id, name, content from " . TABLE_NEWS . " where id = $id");
  //$news = tep_db_fetch_array($news_query_raw);
?>
      <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
       <form action="<?php echo tep_href_link('news.php', 'action=edit&id=' . $id); ?>" method="post">
         <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
<?php

    for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
?>
          <tr>
            <td class="main"><?php if ($i == 0) echo HEADING_NAME; ?></td>
            <td class="main"><?php echo tep_image(DIR_WS_CATALOG_LANGUAGES . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('news_name[' . $languages[$i]['id'] . ']', tep_get_news_name($id, $languages[$i]['id'])); ?></td>
          </tr>
<?php
    }
?>
          <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
          </tr>
<?php
    for ($i=0, $n=sizeof($languages); $i<$n; $i++) {
?>
          <tr>
            <td class="main" valign="top"><?php if ($i == 0) echo HEADING_CONTENT; ?></td>
            <td><table border="0" cellspacing="0" cellpadding="0">
              <tr>
                <td class="main" valign="top"><?php echo tep_image(DIR_WS_CATALOG_LANGUAGES . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], $languages[$i]['name']); ?>&nbsp;</td>
                <td class="main"><?php echo tep_draw_textarea_field_ckeditor('news_content[' . $languages[$i]['id'] . ']', 'soft', '70', '15', tep_get_news_content($id, $languages[$i]['id'])); ?></td>
              </tr>
            </table></td>
          </tr>
<?php
    }
?>

            <tr>
            <input type="hidden" name="id" value="<?php echo $id; ?>">
            <input type="hidden" name="edit_news" value="edit">
            <td>&nbsp;</td>
            <td align="left"><input type="submit" value="Edit"></form>
            <form action="<?php echo tep_href_link('news.php'); ?>" method="post">
            <input type="hidden" name="id" value="<?php echo $id; ?>">
            <input type="hidden" name="delete" value="1">
            <input type="submit" value="Delete"></form></td>
            </tr>
            <tr>
        <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
      </tr>
<?php
   } elseif ($_GET['action'] == 'edit_reply') {
     $id = tep_db_prepare_input($_GET['id']);
     $reply_query = tep_db_query("select news_id from " . TABLE_NEWS_REPLYS . " where news_id = '" . (int)$id . "'");
    $reply = tep_db_fetch_array($reply_query);
   if ($reply == true)  { 

     $reply_query2 = tep_db_query("select * from " . TABLE_NEWS_REPLYS . " where news_id = '" . (int)$id . "'");
      while ($reply2 = tep_db_fetch_array($reply_query2)) {
?>
            <form action="<?php echo tep_href_link('news.php', 'action=edit_reply&id=' . $id); ?>" method="post">
            <tr>
            <td class="main"><?php echo HEADING_NAME; ?></td>
            <td class="main"><input type="text" name="name" size="70" value="<?php echo $reply2['name']; ?>"></td>
            </tr>
            <tr>
            <td class="main"><?php echo HEADING_USERNAME; ?></td>
            <td class="main"><input type="text" name="username" size="70" value="<?php echo $reply2['username']; ?>"></td>
            </tr>
            <tr>
            <td class="main"><?php echo HEADING_EMAIL; ?></td>
            <td class="main"><?php echo $reply2['email']; ?></td>
            </tr>
            <tr valign="top">
            <td class="main"><?php echo HEADING_CONTENT4; ?></td>
            <td class="main"><TEXTAREA NAME="content" COLS=70 ROWS=10><?php echo $reply2['content']; ?></TEXTAREA></td>
            </tr>
<?php
          $checked = null;
        if ($reply2['approved'] == '1') { $checked = 'checked="checked"'; }
?>
            <tr valign="top">
            <td class="main"><?php echo HEADING_APPROVED; ?></td>
            <td class="main"><input type="checkbox" name="approved" value="1" <?php echo $checked; ?>></td>
            </tr>
            <tr>
            <input type="hidden" name="id" value="<?php echo $reply2['id']; ?>">
            <td>&nbsp;</td>
            <td align="left"><input type="submit" value="Edit"></form>
            <form action="<?php echo tep_href_link('news.php', 'action=delete_reply&id=' . $id); ?>" method="post">
            <input type="hidden" name="id" value="<?php echo $reply2['id']; ?>">
            <input type="submit" value="Delete"></form></td>
            </tr>
            <tr>
        <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '100%', '10'); ?></td>
      </tr>
<?php
}
}
} else {
?>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr class="dataTableHeadingRow">
                <td class="dataTableHeadingContent"><?php echo HEADING_DATE_CREATED; ?></td>
                <td class="dataTableHeadingContent"><?php echo HEADING_NAME; ?></td>
                 <td class="dataTableHeadingContent"><?php echo HEADING_REPLY; ?></td>
                <td class="dataTableHeadingContent" align="right"></td>
              </tr>
<?php

  if (isset($_GET['page']) && ($_GET['page'] > 1)) $rows = $_GET['page'] * MAX_DISPLAY_SEARCH_RESULTS - MAX_DISPLAY_SEARCH_RESULTS;
  $rows = 0;
  $products_query_raw = "select n.id, n.date_created, n.replys, nd.name, nd.content from " . TABLE_NEWS . " n, " . TABLE_NEWS_DESC . " nd where nd.language_id = '" . (int)$languages_id . "' and n.id = nd.news_id";
  if ($_GET['month'] == true) {
      $products_query_raw .= " and n.month_date = '" . $_GET['month'] . "'";
}
    $products_query_raw .= " order by n.date_created DESC";
  $products_split = new splitPageResults($_GET['page'], MAX_DISPLAY_SEARCH_RESULTS, $products_query_raw, $products_query_numrows);
  $products_query = tep_db_query($products_query_raw);
  while ($products = tep_db_fetch_array($products_query)) {
   

    if (strlen($rows) < 2) {
      $rows = '0' . $rows;
    }

?>
              <tr class="dataTableRow">
                <td class="dataTableContent"><?php echo tep_date_short($products['date_created']); ?></td>
                <td class="dataTableContent"><u><?php echo '<a href="' . tep_href_link('news.php', 'action=edit&id=' . $products['id'] . '&page=' . $_GET['page'], 'NONSSL') . '">' . $products['name'] . '</a></u>'; ?></td>
                <td class="dataTableContent"><u><?php if ($products['replys'] > 0) echo '<a href="' . tep_href_link('news.php', 'action=edit_reply&id=' . $products['id'] . '&page=' . $_GET['page'], 'NONSSL') . '">' . HEADING_REPLY . '</a></u>'; ?></td>
                <td class="dataTableContent" align="left" valign="bottom"><?php echo HEADING_REPLIES; ?> <?php echo $products['replys']; ?>&nbsp;<?php if (tep_reply($products['id'])) echo tep_image('images/news.gif'); ?></td>
              </tr>
<?php
   $rows++;
  }
?>
            </table></td>
          </tr>
          <tr>
            <td colspan="3"><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr>
                <td class="smallText" valign="top"><?php echo $products_split->display_count($products_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, $_GET['page'], TEXT_DISPLAY_NUMBER_OF_PRODUCTS); ?></td>
                <td class="smallText" align="right"><?php echo $products_split->display_links($products_query_numrows, MAX_DISPLAY_SEARCH_RESULTS, MAX_DISPLAY_PAGE_LINKS, $_GET['page']); ?></td>
              </tr>
              <tr>
              <td align="right" class="main">
              <?php echo '<a href="' . tep_href_link('news.php', 'action=new"><u>' . NEW_ARTICLE . '</u></a>'); ?></td>
             </tr>
            </table></td>
          </tr>
        </table></td>
      </tr>
    </table></td>
<!-- body_text_eof //-->
  </tr>
</table>
<!-- body_eof //-->
<?php
}
?>

<!-- footer //-->
<?php require('includes/footer.php'); ?>
<!-- footer_eof //-->
</body>
</html>
<?php require('includes/application_bottom.php'); ?>
