<?php
// Test script for CSV database import functionality

// Set up the application environment
$path['fs_app_root'] = __DIR__ . '/';
require_once $path['fs_app_root'] . 'system/startup_sequence.php';

// Test CSV data
$test_csv = "<PERSON>,Age,Email,City
<PERSON>,30,<EMAIL>,New York
<PERSON>,25,<EMAIL>,Los Angeles
<PERSON>,35,<EMAIL>,Chicago
<PERSON>,28,<EMAIL>,<PERSON>,42,<EMAIL>,Phoenix";

echo "Testing CSV Database Import Functionality\n";
echo "==========================================\n\n";

try {
    // Test 1: Import CSV to database
    echo "1. Testing CSV import to database...\n";
    $result = csv_database_manager::import_csv_to_table($test_csv, [
        'has_header' => true,
        'delimiter' => ','
    ]);
    
    if (isset($result['success']) && $result['success']) {
        echo "✓ Import successful!\n";
        echo "  Table name: {$result['table_name']}\n";
        echo "  Rows imported: {$result['row_count']}\n";
        echo "  Headers: " . implode(', ', $result['headers']) . "\n\n";
        
        $table_name = $result['table_name'];
        
        // Test 2: Query the data
        echo "2. Testing data query...\n";
        $query_result = csv_database_manager::query_table($table_name, [
            'page' => 1,
            'per_page' => 10
        ]);
        
        if (isset($query_result['success']) && $query_result['success']) {
            echo "✓ Query successful!\n";
            echo "  Total rows: {$query_result['total_rows']}\n";
            echo "  Columns: " . implode(', ', $query_result['columns']) . "\n";
            echo "  Sample data:\n";
            
            foreach (array_slice($query_result['rows'], 0, 3) as $row) {
                echo "    - " . implode(' | ', array_values($row)) . "\n";
            }
            echo "\n";
        } else {
            echo "✗ Query failed: " . ($query_result['error'] ?? 'Unknown error') . "\n\n";
        }
        
        // Test 3: Search functionality
        echo "3. Testing search functionality...\n";
        $search_result = csv_database_manager::query_table($table_name, [
            'page' => 1,
            'per_page' => 10,
            'search' => 'John'
        ]);
        
        if (isset($search_result['success']) && $search_result['success']) {
            echo "✓ Search successful!\n";
            echo "  Found {$search_result['total_rows']} rows matching 'John'\n";
            if (!empty($search_result['rows'])) {
                foreach ($search_result['rows'] as $row) {
                    echo "    - " . implode(' | ', array_values($row)) . "\n";
                }
            }
            echo "\n";
        } else {
            echo "✗ Search failed: " . ($search_result['error'] ?? 'Unknown error') . "\n\n";
        }
        
        // Test 4: List tables
        echo "4. Testing table listing...\n";
        $list_result = csv_database_manager::list_tables();
        
        if (isset($list_result['success']) && $list_result['success']) {
            echo "✓ Table listing successful!\n";
            echo "  Found " . count($list_result['tables']) . " CSV tables:\n";
            foreach ($list_result['tables'] as $table) {
                echo "    - {$table}\n";
            }
            echo "\n";
        } else {
            echo "✗ Table listing failed: " . ($list_result['error'] ?? 'Unknown error') . "\n\n";
        }
        
        // Test 5: Clean up (delete test table)
        echo "5. Testing table deletion...\n";
        $delete_result = csv_database_manager::delete_table($table_name);
        
        if (isset($delete_result['success']) && $delete_result['success']) {
            echo "✓ Table deletion successful!\n";
            echo "  {$delete_result['message']}\n\n";
        } else {
            echo "✗ Table deletion failed: " . ($delete_result['error'] ?? 'Unknown error') . "\n\n";
        }
        
    } else {
        echo "✗ Import failed: " . ($result['error'] ?? 'Unknown error') . "\n\n";
    }
    
    echo "Test completed successfully!\n";
    
} catch (Exception $e) {
    echo "✗ Test failed with exception: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
