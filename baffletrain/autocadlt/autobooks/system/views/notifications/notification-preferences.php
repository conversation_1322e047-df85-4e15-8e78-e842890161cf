<?php
// This template displays the notification preferences form
// Define available notification types
$notificationTypes = [
    'system' => 'System Notifications',
    'order' => 'Order Updates',
    'quote' => 'Quote Updates',
    'subscription' => 'Subscription Updates',
    'customer' => 'Customer Updates'
];
?>
<div class="notification-preferences p-4">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Notification Preferences</h3>
    
    <div class="space-y-4">
        <?php foreach ($notificationTypes as $type => $label): ?>
            <div class="flex items-center justify-between">
                <label for="pref-<?= $type ?>" class="text-sm font-medium text-gray-700"><?= $label ?></label>
                <div class="ml-4 flex-shrink-0">
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" 
                               id="pref-<?= $type ?>"
                               class="sr-only peer"
                               <?= isset($preferences[$type]) && $preferences[$type] ? 'checked' : '' ?>
                               hx-post="<?= APP_ROOT ?>api/notifications/update_preference"
                               hx-vals='{"type": "<?= $type ?>", "enabled": this.checked}'
                               hx-swap="none"
                               hx-trigger="change">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>
